proxies:
  - alpn:
      - h3
    auth-str: dongtaiwang.com
    auth_str: dongtaiwang.com
    down: 55 Mbps
    name: "\U0001F1FA\U0001F1F8美国1 | ⬇️ 2.6MB/s"
    port: 11512
    protocol: udp
    server: *************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria
    udp: true
    up: 11 Mbps
  - auth: dongtaiwang.com
    down: 100 Mbps
    name: "\U0001F1FA\U0001F1F8美国2 | ⬇️ 5.0MB/s"
    password: dongtaiwang.com
    port: 63015
    server: *************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria2
    udp: true
    up: 100 Mbps
  - alpn:
      - h3
    auth-str: dongtaiwang.com
    auth_str: dongtaiwang.com
    down: 100
    name: "\U0001F1FA\U0001F1F8美国3 | ⬇️ 1.5MB/s"
    port: 17549
    protocol: udp
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国4 | ⬇️ 4.1MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /Telegram @MxlShare @WangCai2 /
  - auth: dongtaiwang.com
    down: 100 Mbps
    name: "\U0001F1F3\U0001F1F1荷兰1 | ⬇️ 9.3MB/s"
    password: dongtaiwang.com
    port: 50066
    server: ************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    udp: true
    up: 100 Mbps
  - name: "\U0001F1FA\U0001F1F8美国5 | ⬇️ 3.0MB/s"
    network: ws
    port: 443
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: e6236ebc-7334-4bed-977f-0c20bcdfcc00
    ws-opts:
      headers:
        Host: barayeiranmahsang.ghormehsabzi.dpdns.org
      path: /
    servername: barayeiranmahsang.ghormehsabzi.dpdns.org
  - auth: a16d62f6-048d-11f0-af5a-f23c93136cb3
    name: "\U0001F1FA\U0001F1F8美国6 | ⬇️ 6.4MB/s"
    password: a16d62f6-048d-11f0-af5a-f23c93136cb3
    port: 1743
    server: b9c3912b-t07z40-t1l1nh-d6ar.la.shifen.uk
    type: hysteria2
    udp: true
    sni: b9c3912b-t07z40-t1l1nh-d6ar.la.shifen.uk
  - name: "\U0001F1FA\U0001F1F8美国7 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /TelegramU0001F1E8U0001F1F3
    servername: sk.laoyoutiao.link
  - name: "\U0001F1FA\U0001F1F8美国8 | ⬇️ 3.0MB/s"
    network: ws
    port: 8880
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    servername: Telegram-channel-WangCai2
  - name: "\U0001F1FA\U0001F1F8美国9 | ⬇️ 1.5MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /TelegramU0001F1E8U0001F1F3
  - auth: 5CBqBh6MeDq6GajcilBiDg==
    name: "\U0001F1FA\U0001F1F8美国10 | ⬇️ 1.1MB/s"
    password: 5CBqBh6MeDq6GajcilBiDg==
    port: 61001
    server: 192-227-152-86.nip.io
    skip-cert-verify: true
    sni: 192-227-152-86.nip.io
    type: hysteria2
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国11 | ⬇️ 3.4MB/s"
    network: ws
    port: 443
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: 7a80a8d9-92f9-4f0a-8352-9005a8215ab8
    ws-opts:
      headers:
        Host: rAyAn-11.LeIlA.DpDnS.OrG
      path: /@rayan_config
    servername: rAyAn-11.LeIlA.DpDnS.OrG
  - name: "\U0001F300其他1-未识别 | ⬇️ 5.0MB/s"
    network: ws
    port: 80
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: /Telegram
  - auth: uyzMwZOG2JFOEXhjBnMX7bkf3o
    name: "\U0001F1FA\U0001F1F8美国12 | ⬇️ 3.2MB/s"
    password: uyzMwZOG2JFOEXhjBnMX7bkf3o
    port: 24742
    server: *************
    skip-cert-verify: true
    sni: bing.com
    type: hysteria2
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国13 | ⬇️ 2.1MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国14 | ⬇️ 2.5MB/s"
    network: ws
    port: 443
    server: s.askkf.com
    tls: true
    type: vless
    udp: true
    uuid: 81fae892-37b3-47d5-d047-b44b3395fa38
    ws-opts:
      headers:
        Host: s2.askkf.com
      path: /
    servername: s2.askkf.com
  - name: "\U0001F1FA\U0001F1F8美国15 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /Telegram @WangCai2 /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国16 | ⬇️ 3.7MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国17 | ⬇️ 4.9MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2"
  - name: "\U0001F1FA\U0001F1F8美国18 | ⬇️ 2.3MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /TelegramU0001F1E8U0001F1F3
  - name: "\U0001F1FA\U0001F1F8美国19 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: >-
        /TelegramU0001F1E8U0001F1F3?ed=2048@Evay_vpn----@Evay_vpn----@Evay_vpn----@Evay_vpn----@Evay_vpn----@Evay_vpn----@Evay_vpn----@Evay_vpn----@Evay_vpn
  - name: "\U0001F1FA\U0001F1F8美国20 | ⬇️ 1.6MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: /TelegramU0001F1E8U0001F1F3
  - name: "\U0001F1FA\U0001F1F8美国21 | ⬇️ 1.7MB/s"
    network: ws
    port: 80
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F300其他2-AM | ⬇️ 2.4MB/s"
    network: ws
    port: 443
    server: www.speedtest.net
    tls: true
    type: vless
    udp: true
    uuid: db7a5e4e-05b8-4111-ab6c-11a1ef87163d
    ws-opts:
      headers:
        Host: rayan-fast.parmida.dpdns.org
      path: /
    servername: rayan-fast.parmida.dpdns.org
  - name: "\U0001F1FA\U0001F1F8美国22 | ⬇️ 2.0MB/s"
    network: ws
    port: 8080
    server: csgo.com
    tls: false
    type: vless
    udp: true
    uuid: 9c67ebbe-d620-4b6d-ad82-1ebc675e22c6
    ws-opts:
      headers:
        Host: cdn-node-oss-99.paofu.de
      path: /profile/telegram@ssrsub
    servername: cdn-node-oss-99.paofu.de
  - client-fingerprint: random
    name: "\U0001F1EB\U0001F1EE芬兰1 | ⬇️ 1.1MB/s"
    network: ws
    port: 443
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: b28eb861-1748-4200-ba72-278669edc33b
    ws-opts:
      headers:
        Host: barayeiranmahsang.mahmoodchitooz.dpdns.org
      path: /Tel-@V2ray_Alpha/?ed=2560
    servername: barayeiranmahsang.mahmoodchitooz.dpdns.org
  - name: "\U0001F1FA\U0001F1F8美国23 | ⬇️ 2.2MB/s"
    network: ws
    port: 443
    server: f3.askkf.com
    tls: true
    type: vless
    udp: true
    uuid: 81fae892-37b3-47d5-d047-b44b3395fa38
    ws-opts:
      headers:
        Host: s2.askkf.com
      path: /
    servername: s2.askkf.com
  - name: "\U0001F1FA\U0001F1F8美国24 | ⬇️ 2.5MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /TelegramU0001F1E8U0001F1F3
    servername: sk.laoyoutiao.link
  - name: "\U0001F1E9\U0001F1EA德国1 | ⬇️ 3.9MB/s"
    network: ws
    port: 8080
    server: hh.somo44.sbs
    tls: false
    type: vless
    udp: true
    uuid: 0043c18f-c3a8-4762-9815-b54989eabdd5
    ws-opts:
      headers:
        Host: Hoo.somo44.sbs
      path: /hhvghhv
  - auth: dEvt5wSHGhobuYBR8kdo4aBvvqQ
    name: "\U0001F1FA\U0001F1F8美国25 | ⬇️ 2.2MB/s"
    password: dEvt5wSHGhobuYBR8kdo4aBvvqQ
    port: 6526
    server: ***************
    skip-cert-verify: true
    sni: bing.com
    type: hysteria2
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国26 | ⬇️ 2.4MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: yd.laoyoutiao.link
      path: /TelegramU0001F1E8U0001F1F3
  - name: "\U0001F300其他3-未识别 | ⬇️ 4.9MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: yd.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560fp=chrome"
  - name: "\U0001F1FA\U0001F1F8美国27 | ⬇️ 5.0MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: bitget1.asdasd.click
  - name: "\U0001F1FA\U0001F1F8美国28 | ⬇️ 2.4MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 7a80a8d9-92f9-4f0a-8352-9005a8215ab8
    ws-opts:
      headers:
        Host: rAyAn-11.LeIlA.DpDnS.OrG
      path: /@rayan_config
    servername: rAyAn-11.LeIlA.DpDnS.OrG
  - name: "\U0001F1FA\U0001F1F8美国29 | ⬇️ 4.4MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /Telegram @MxlShare @WangCai2 /
  - name: "\U0001F1FA\U0001F1F8美国30 | ⬇️ 3.1MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /Telegram@MxlShare@WangCai2/?ed
    servername: sk.laoyoutiao.link
  - name: "\U0001F1E9\U0001F1EA德国2 | ⬇️ 4.1MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
    ws-opts:
      headers:
        Host: VngSuPpLY.IP-DdnS.com
      path: /1ycR2zb3KeELWRha
    servername: VngSuPpLY.IP-DdnS.com
  - name: "\U0001F1FA\U0001F1F8美国31 | ⬇️ 1.1MB/s"
    network: ws
    port: 80
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 7248e825-887c-48b9-83bc-c26bc6392bf8
    ws-opts:
      headers:
        Host: xxcdvfgt.191268.xyz
      path: /W02wBrOOqlSUywV3ibrzzKXJGy3S1
    servername: xxcdvfgt.191268.xyz
  - name: "\U0001F300其他4-未识别 | ⬇️ 1.3MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 583ceab3-**************-9bedc625ad4e
    ws-opts:
      headers:
        Host: ip.langmanshanxi.top
      path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    servername: Telegram-channel-WangCai2
  - client-fingerprint: random
    name: "\U0001F1E9\U0001F1EA德国3 | ⬇️ 5.2MB/s"
    network: ws
    port: 80
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: f278037e-ce36-4634-9b4d-fca93e345a35
    ws-opts:
      headers:
        Host: barayeiran.mahiozonboroon.qzz.io
      path: /vpnowl-vpnowl-vpnowl-vpnowl?ed=2560
    servername: barayeiran.mahiozonboroon.qzz.io
  - name: "\U0001F1E8\U0001F1E6加拿大1 | ⬇️ 5.0MB/s"
    network: ws
    port: 80
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 3ac2de34-47c5-4dd5-afc0-8fb4b05d4077
    ws-opts:
      headers:
        Host: blaze-can-118.blazecanada.site
      path: /
    servername: blaze-can-118.blazecanada.site
  - name: "\U0001F1E9\U0001F1EA德国4 | ⬇️ 3.5MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
    ws-opts:
      headers:
        Host: vngsupply.ip-ddns.com
      path: /J5aLQOY1R9ONWYCM?ed=2560
    servername: vngsupply.ip-ddns.com
proxy-groups:
  - name: "\U0001F680 节点选择"
    type: select
    proxies:
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F680 手动切换"
    include-all: true
    type: select
  - name: ♻️ 自动选择
    type: url-test
    include-all: true
    interval: 300
    tolerance: 50
  - name: "\U0001F4F2 电报消息"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F4AC Ai平台"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F4F9 油管视频"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F3A5 奈飞视频"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F3A5 奈飞节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F4FA 巴哈姆特"
    type: select
    proxies:
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F680 节点选择"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F4FA 哔哩哔哩"
    type: select
    proxies:
      - "\U0001F3AF 全球直连"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
  - name: "\U0001F30D 国外媒体"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F30F 国内媒体"
    type: select
    proxies:
      - DIRECT
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F4E2 谷歌FCM"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: Ⓜ️ 微软Bing
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: Ⓜ️ 微软云盘
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: Ⓜ️ 微软服务
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - DIRECT
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F34E 苹果服务"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F3AE 游戏平台"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F3B6 网易音乐"
    type: select
    include-all: true
    filter: (?i)网易|音乐|NetEase|Music
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
  - name: "\U0001F3AF 全球直连"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
  - name: "\U0001F6D1 广告拦截"
    type: select
    proxies:
      - REJECT
      - DIRECT
  - name: "\U0001F343 应用净化"
    type: select
    proxies:
      - REJECT
      - DIRECT
  - name: "\U0001F41F 漏网之鱼"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - DIRECT
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F1ED\U0001F1F0 香港节点"
    include-all: true
    filter: (?i)港|HK|hk|Hong Kong|HongKong|hongkong
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1EF\U0001F1F5 日本节点"
    include-all: true
    filter: (?i)日本|川日|东京|大阪|泉日|埼玉|沪日|深日|JP|Japan
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1FA\U0001F1F2 美国节点"
    include-all: true
    filter: (?i)美|波特兰|达拉斯|俄勒冈|凤凰城|费利蒙|硅谷|拉斯维加斯|洛杉矶|圣何塞|圣克拉拉|西雅图|芝加哥|US|United States
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1E8\U0001F1F3 台湾节点"
    include-all: true
    filter: (?i)台|新北|彰化|TW|Taiwan
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1F8\U0001F1EC 狮城节点"
    include-all: true
    filter: (?i)新加坡|坡|狮城|SG|Singapore
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1F0\U0001F1F7 韩国节点"
    include-all: true
    filter: (?i)KR|Korea|KOR|首尔|韩|韓
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F3A5 奈飞节点"
    include-all: true
    filter: (?i)NF|奈飞|解锁|Netflix|NETFLIX|Media
    type: select
rule-providers:
  LocalAreaNetwork:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/LocalAreaNetwork.list
    path: ./ruleset/LocalAreaNetwork.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  UnBan:
    url: 'https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/UnBan.list'
    path: ./ruleset/UnBan.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  BanAD:
    url: 'https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/BanAD.list'
    path: ./ruleset/BanAD.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  BanProgramAD:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/BanProgramAD.list
    path: ./ruleset/BanProgramAD.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  GoogleFCM:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/GoogleFCM.list
    path: ./ruleset/GoogleFCM.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  GoogleCN:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/GoogleCN.list
    path: ./ruleset/GoogleCN.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  SteamCN:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/SteamCN.list
    path: ./ruleset/SteamCN.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Bing:
    url: 'https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Bing.list'
    path: ./ruleset/Bing.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  OneDrive:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/OneDrive.list
    path: ./ruleset/OneDrive.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Microsoft:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Microsoft.list
    path: ./ruleset/Microsoft.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Apple:
    url: 'https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Apple.list'
    path: ./ruleset/Apple.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Telegram:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Telegram.list
    path: ./ruleset/Telegram.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  AI:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/AI.list
    path: ./ruleset/AI.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  OpenAi:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/OpenAi.list
    path: ./ruleset/OpenAi.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  NetEaseMusic:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/NetEaseMusic.list
    path: ./ruleset/NetEaseMusic.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Epic:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Epic.list
    path: ./ruleset/Epic.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Origin:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Origin.list
    path: ./ruleset/Origin.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Sony:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Sony.list
    path: ./ruleset/Sony.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Steam:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Steam.list
    path: ./ruleset/Steam.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Nintendo:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Nintendo.list
    path: ./ruleset/Nintendo.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  YouTube:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/YouTube.list
    path: ./ruleset/YouTube.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Netflix:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Netflix.list
    path: ./ruleset/Netflix.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Bahamut:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Bahamut.list
    path: ./ruleset/Bahamut.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  BilibiliHMT:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/BilibiliHMT.list
    path: ./ruleset/BilibiliHMT.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Bilibili:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Bilibili.list
    path: ./ruleset/Bilibili.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ChinaMedia:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaMedia.list
    path: ./ruleset/ChinaMedia.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ProxyMedia:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ProxyMedia.list
    path: ./ruleset/ProxyMedia.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ProxyGFWlist:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ProxyGFWlist.list
    path: ./ruleset/ProxyGFWlist.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ChinaDomain:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaDomain.list
    path: ./ruleset/ChinaDomain.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ChinaCompanyIp:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaCompanyIp.list
    path: ./ruleset/ChinaCompanyIp.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Download:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Download.list
    path: ./ruleset/Download.list
    behavior: classical
    interval: 86400
    format: text
    type: http
rules:
  - "PROCESS-NAME,subs-check.exe,\U0001F3AF 全球直连"
  - "PROCESS-NAME,subs-check,\U0001F3AF 全球直连"
  - "RULE-SET,LocalAreaNetwork,\U0001F3AF 全球直连"
  - "RULE-SET,UnBan,\U0001F3AF 全球直连"
  - "RULE-SET,BanAD,\U0001F6D1 广告拦截"
  - "RULE-SET,BanProgramAD,\U0001F343 应用净化"
  - "RULE-SET,GoogleFCM,\U0001F4E2 谷歌FCM"
  - "RULE-SET,GoogleCN,\U0001F3AF 全球直连"
  - "RULE-SET,SteamCN,\U0001F3AF 全球直连"
  - 'RULE-SET,Bing,Ⓜ️ 微软Bing'
  - 'RULE-SET,OneDrive,Ⓜ️ 微软云盘'
  - 'RULE-SET,Microsoft,Ⓜ️ 微软服务'
  - "RULE-SET,Apple,\U0001F34E 苹果服务"
  - "RULE-SET,Telegram,\U0001F4F2 电报消息"
  - "RULE-SET,AI,\U0001F4AC Ai平台"
  - "RULE-SET,NetEaseMusic,\U0001F3B6 网易音乐"
  - "RULE-SET,Epic,\U0001F3AE 游戏平台"
  - "RULE-SET,Origin,\U0001F3AE 游戏平台"
  - "RULE-SET,Sony,\U0001F3AE 游戏平台"
  - "RULE-SET,Steam,\U0001F3AE 游戏平台"
  - "RULE-SET,Nintendo,\U0001F3AE 游戏平台"
  - "RULE-SET,YouTube,\U0001F4F9 油管视频"
  - "RULE-SET,Netflix,\U0001F3A5 奈飞视频"
  - "RULE-SET,Bahamut,\U0001F4FA 巴哈姆特"
  - "RULE-SET,BilibiliHMT,\U0001F4FA 哔哩哔哩"
  - "RULE-SET,Bilibili,\U0001F4FA 哔哩哔哩"
  - "RULE-SET,ChinaMedia,\U0001F30F 国内媒体"
  - "RULE-SET,ProxyMedia,\U0001F30D 国外媒体"
  - "RULE-SET,ProxyGFWlist,\U0001F680 节点选择"
  - "RULE-SET,ChinaDomain,\U0001F3AF 全球直连"
  - "RULE-SET,ChinaCompanyIp,\U0001F3AF 全球直连"
  - "RULE-SET,Download,\U0001F3AF 全球直连"
  - "GEOIP,CN,\U0001F3AF 全球直连"
  - "MATCH,\U0001F41F 漏网之鱼"
