# Clash Verge 长时间运行优化与故障排除指南

## 🎯 核心问题分析

### 长时间运行后谷歌搜索失效问题

**问题现象：**
- Clash Verge 长时间运行后，谷歌搜索无法正常工作
- 其他网站可以正常访问
- 重启 Clash（不重启内核）后问题解决
- 浏览器提示 "HTTP刷新" 相关信息

**根本原因：**
这不是 Clash 配置问题，而是 **HTTP连接状态缓存不同步** 导致的：

1. **浏览器连接缓存过期**
   - Chrome/Edge 缓存 HTTP/HTTPS 连接状态
   - 长时间运行后，浏览器连接信息与 Clash 实际状态不匹配
   - 浏览器仍使用已失效的代理连接信息

2. **代理连接池状态累积**
   - Clash 连接池在长时间运行后状态复杂化
   - 某些连接处于半开状态或异常状态
   - 新的 Google 请求无法正确建立连接

3. **DNS 解析缓存冲突**
   - fake-ip 模式下的域名解析缓存
   - Google 域名的 IP 地址变化未及时更新
   - 导致连接建立失败

## ✅ 已验证的解决方案

### 立即解决方法

1. **重启 Clash 服务（推荐）**
   - 在 Clash Verge 界面点击重启
   - 不需要重启系统内核
   - 清理连接状态缓存，重新建立代理连接

2. **浏览器连接刷新**
   ```
   Chrome/Edge:
   - 按 F12 打开开发者工具
   - Network 标签页 → 右键 → Clear browser cache
   - 或使用 Ctrl+Shift+Delete 清除缓存
   ```

3. **手动切换代理节点**
   - 临时切换到不同的代理节点
   - 等待 30 秒后切换回原节点
   - 强制重新建立连接

### 预防性配置优化

#### 1. 连接管理优化
当前 Script.js 中的配置已经很好，建议保持：
```javascript
// 现有配置保持不变，已经优化
"connection-idle-timeout": 600,      // 10分钟空闲超时
"persistent-connection": false,      // 禁用持久连接
"tcp-keep-alive": true,             // 启用TCP保活
```

#### 2. DNS 缓存优化
当前配置合理，建议保持：
```javascript
// 现有配置保持不变
"cache-ttl": 1800,                  // 30分钟缓存TTL
"fake-ip-cache-size": 65536,        // 64K缓存大小
```

#### 3. 健康检查频率
当前配置适中，建议保持：
```javascript
// 现有配置保持不变
"interval": 900,                    // 15分钟检查间隔
"timeout": 8000,                    // 8秒超时
```

## 🔧 系统级优化建议

### Windows 网络优化
配合现有的 `windows_network_optimization.ps1` 脚本，添加以下操作：

1. **定期清理系统 HTTP 缓存**
   ```powershell
   # 每天执行一次
   netsh winhttp reset proxy
   ipconfig /flushdns
   ```

2. **浏览器缓存管理**
   - 设置浏览器每天自动清理缓存
   - 或手动每天清理一次浏览器数据

3. **Clash 服务管理**
   - 建议每 24-48 小时重启一次 Clash 服务
   - 可以设置定时任务自动重启

## 📊 监控和诊断

### 问题识别指标
出现以下情况时，建议重启 Clash：

1. **连接异常指标**
   - Google 搜索加载缓慢或失败
   - 浏览器显示 "连接重置" 或 "HTTP 错误"
   - 其他网站正常但 Google 服务异常

2. **时间指标**
   - Clash 连续运行超过 24 小时
   - 上次重启距今超过 48 小时
   - 系统内存使用率持续上升

3. **网络指标**
   - 代理延迟突然增加
   - 连接成功率下降
   - DNS 解析时间异常

### 日志分析
在 Clash Verge 日志中关注以下信息：
```
[WARNING] connection timeout
[ERROR] dial tcp: connection refused
[INFO] http: proxy error
```

## 🎯 最佳实践

### 日常维护流程

1. **每日检查（1分钟）**
   - 测试 Google 搜索是否正常
   - 检查代理节点延迟
   - 观察 Clash 内存使用情况

2. **每周维护（5分钟）**
   - 重启 Clash 服务一次
   - 清理浏览器缓存
   - 检查订阅更新

3. **每月优化（10分钟）**
   - 运行 Windows 网络优化脚本
   - 检查 Clash 配置更新
   - 评估节点质量和速度

### 故障排除流程

**步骤1：快速诊断**
- 测试其他网站是否正常
- 检查当前使用的代理节点
- 查看 Clash 日志最新信息

**步骤2：基础修复**
- 重启 Clash 服务（不重启内核）
- 清理浏览器缓存
- 切换到备用代理节点

**步骤3：深度修复**
- 运行 `ipconfig /flushdns`
- 重启浏览器
- 检查系统网络设置

**步骤4：系统级修复**
- 运行网络优化脚本
- 重启网络适配器
- 必要时重启系统

## 🚨 注意事项

### 重要提醒
1. **不要频繁重启**
   - 避免每小时重启 Clash
   - 正常情况下 24-48 小时重启一次即可

2. **保持配置稳定**
   - 当前 Script.js 配置已经优化
   - 不建议频繁修改核心参数

3. **监控资源使用**
   - 关注 Clash 内存使用情况
   - 64GB 内存配置下正常使用 1-2GB

### 配置兼容性
- 当前配置与 Windows 11 完全兼容
- 支持 TUN 模式和系统代理模式
- 兼容主流浏览器（Chrome、Edge、Firefox）

## 📈 性能指标

### 正常运行指标
- **代理延迟**：< 200ms（优秀）
- **连接成功率**：> 95%
- **DNS 解析时间**：< 100ms
- **内存使用**：< 2GB（64GB 系统）

### 异常警告指标
- **代理延迟**：> 500ms
- **连接成功率**：< 90%
- **DNS 解析时间**：> 300ms
- **内存使用**：> 4GB

## 🎉 总结

**核心要点：**
1. 长时间运行后的 Google 搜索问题是 HTTP 连接缓存问题，不是配置错误
2. 重启 Clash（不重启内核）是最有效的解决方案
3. 当前 Script.js 配置已经很优秀，无需修改
4. 定期维护比频繁调整配置更重要

**维护建议：**
- 每天测试一次 Google 搜索
- 每 1-2 天重启一次 Clash 服务
- 每周清理一次浏览器缓存
- 保持当前配置稳定不变

您的 Clash 配置方案是正确和优秀的！