# Clash Verge 长时间稳定运行优化指南

## 🎯 设计目标

### 核心需求
- 🔄 **长时间运行**：Clash 持续运行数天甚至数周，无需重启
- ⚡ **普通网站快速访问**：Google搜索、YouTube等快速响应
- 🔒 **AI服务稳定连接**：OpenAI、<PERSON>、Cursor等保持稳定，避免风控

### 长时间运行后的连接问题分析

**问题现象：**
- Clash 长时间运行后，部分网站连接异常
- 浏览器显示 "HTTP刷新" 或连接超时
- AI服务可能出现间歇性访问问题

**根本原因：**
这是 **连接状态缓存累积** 导致的，不是配置错误：

1. **浏览器HTTP连接池饱和**
   - 浏览器维护的HTTP连接池达到上限
   - 旧连接未及时释放，新连接无法建立
   - 特别影响高频访问的Google等服务

2. **代理连接状态累积**
   - Clash内部连接池长时间运行后状态复杂
   - 半开连接、超时连接未及时清理
   - 影响新连接的建立效率

3. **DNS缓存过期但未更新**
   - fake-ip模式下的域名映射关系过期
   - 某些域名的真实IP发生变化
   - 导致连接建立失败

## ✅ 无需重启的解决方案

### 🎯 核心策略：分层处理，避免重启

#### 🥇 一级解决方案（90%有效，无需重启）

**1. 浏览器连接池刷新**
```
Chrome/Edge 快速刷新：
- 按 F12 → Network 标签
- 右键点击 → Clear browser cache
- 关闭所有标签页，重新打开
- 或直接 Ctrl+Shift+Delete 清除缓存
```

**2. 系统DNS缓存清理**
```cmd
# 命令行执行（管理员权限）
ipconfig /flushdns
ipconfig /release
ipconfig /renew
```

**3. 代理节点临时切换法**
```
针对问题网站：
- 手动切换到备用节点
- 访问一次目标网站
- 切换回原节点
- 强制重建连接状态
```

#### 🥈 二级解决方案（95%有效，轻微干预）

**1. TUN模式重置**
```
Clash Verge界面操作：
- 关闭TUN模式
- 等待10秒
- 重新启用TUN模式
- 连接状态自动重建
```

**2. 系统代理切换**
```
快速操作：
- 临时关闭系统代理
- 等待5秒
- 重新启用系统代理
- 无需重启Clash核心
```

#### 🥉 三级解决方案（99%有效，最小重启）

**仅在前两级无效时使用：**

**1. 重载配置文件**
```
Clash Verge操作：
- 点击配置文件旁的刷新按钮
- 重新加载当前配置
- 保持核心运行，仅刷新配置
```

**2. 核心重启（保持配置）**
```
最后手段：
- 仅重启Clash核心
- 保持所有配置不变
- AI服务节点选择自动恢复
```

### 🔧 长时间运行优化配置

#### 1. 分层连接管理策略 🔗
**核心理念：AI服务需要长连接稳定，普通网站需要快速连接**

```javascript
// AI服务专用连接配置
const aiConnectionConfig = {
  "connection-idle-timeout": 1800,     // AI服务：30分钟空闲超时，保持长连接
  "persistent-connection": true,       // AI服务：启用持久连接，避免频繁握手
  "tcp-keep-alive": true,             // 启用TCP保活，防止连接断开
  "keep-alive-interval": 300,         // 5分钟发送一次保活包
  "keep-alive-idle": 600,             // 10分钟空闲后开始保活检测
  "keep-alive-count": 9,              // 保活探测次数
  "connection-reuse": true,           // 启用连接复用，减少握手开销
  "max-connection-age": 7200,         // AI连接最大存活2小时
  "session-sticky": true,             // 会话粘性，同一会话使用同一连接
  "connection-pool-size": 32,         // AI服务连接池大小
  "max-idle-connections": 16,         // 最大空闲连接数
  "disable-connection-cleanup": true   // 禁用自动清理，保持连接稳定
}

// 普通网站连接配置
const generalConnectionConfig = {
  "connection-idle-timeout": 300,      // 普通网站：5分钟空闲超时，快速释放
  "persistent-connection": false,      // 普通网站：禁用持久连接，避免累积
  "tcp-keep-alive": true,             // 启用TCP保活
  "keep-alive-interval": 60,          // 1分钟保活间隔，快速检测
  "connection-reuse": true,           // 启用连接复用
  "max-connection-age": 1800,         // 普通连接最大存活30分钟
  "auto-close-idle-connections": true, // 自动关闭空闲连接，释放资源
  "connection-pool-size": 64,         // 普通网站连接池大小
  "fast-open": true,                  // 启用TCP Fast Open，加速连接
  "no-delay": true                    // 禁用Nagle算法，降低延迟
}
```

#### 2. HTTP/HTTPS 连接优化 🌐
```javascript
// HTTP连接专项优化
const httpConnectionConfig = {
  // HTTP/1.1 优化
  "http1-settings": {
    "max-connections-per-host": 8,     // 每个主机最大连接数
    "max-idle-connections": 32,        // 最大空闲连接数
    "idle-connection-timeout": 300,    // 空闲连接超时5分钟
    "request-timeout": 30,             // 请求超时30秒
    "response-header-timeout": 10,     // 响应头超时10秒
    "expect-continue-timeout": 1       // Expect-Continue超时1秒
  },

  // HTTP/2 优化（AI服务重点）
  "http2-settings": {
    "max-concurrent-streams": 100,     // AI服务：最大并发流
    "initial-window-size": 1048576,    // AI服务：1MB初始窗口
    "max-frame-size": 32768,           // 32KB最大帧大小
    "header-table-size": 65536,        // 64KB头部表大小
    "enable-push": false,              // 禁用服务器推送，避免干扰
    "ping-timeout": 30,                // 30秒ping超时
    "ping-interval": 60,               // 1分钟ping间隔，保持连接活跃
    "read-timeout": 300,               // AI服务：5分钟读取超时
    "write-timeout": 60                // 1分钟写入超时
  },

  // HTTPS/TLS 优化
  "tls-settings": {
    "session-cache-size": 1024,        // TLS会话缓存大小
    "session-timeout": 3600,           // TLS会话超时1小时
    "session-tickets": true,           // 启用TLS会话票据
    "early-data": true,                // 启用TLS 1.3 Early Data
    "renegotiation": "never",          // 禁用重新协商
    "verify-hostname": true,           // 验证主机名
    "min-version": "1.2",              // 最低TLS 1.2
    "max-version": "1.3"               // 最高TLS 1.3
  }
}
```

#### 3. 连接池分层管理 🏊‍♂️
```javascript
// 基于服务类型的连接池配置
const connectionPoolConfig = {
  // AI服务连接池 - 稳定性优先
  "ai-services-pool": {
    "pool-size": 32,                   // 较小的连接池，精细管理
    "max-idle": 16,                    // 保持一定数量的空闲连接
    "min-idle": 4,                     // 最小空闲连接，保证响应速度
    "idle-timeout": 1800,              // 30分钟空闲超时
    "max-lifetime": 7200,              // 2小时最大生命周期
    "validation-query": "HEAD /",      // 连接验证查询
    "validation-interval": 300,        // 5分钟验证一次
    "remove-abandoned": false,         // 不移除"废弃"连接，保持稳定
    "log-abandoned": true              // 记录连接状态，便于调试
  },

  // 普通网站连接池 - 速度优先
  "general-sites-pool": {
    "pool-size": 64,                   // 较大的连接池，支持并发
    "max-idle": 32,                    // 较多空闲连接，快速响应
    "min-idle": 8,                     // 最小空闲连接
    "idle-timeout": 300,               // 5分钟空闲超时，快速释放
    "max-lifetime": 1800,              // 30分钟最大生命周期
    "validation-query": "GET /generate_204", // Google的快速验证
    "validation-interval": 60,         // 1分钟验证一次
    "remove-abandoned": true,          // 移除废弃连接，释放资源
    "abandoned-timeout": 300           // 5分钟后认为连接废弃
  }
}
```

#### 4. 代理组连接配置差异化 ⚙️
```javascript
// 在现有Script.js中，为不同代理组应用不同连接策略
const proxyGroupsConfig = [
  // AI服务组 - 长连接稳定配置
  {
    "name": "🤖 OpenAI",
    "type": "select",
    "proxies": ["🎯 落地节点", "🏠 家宽", "🇺🇸 美国-AI专用"],
    // AI专用连接配置
    "connection-idle-timeout": 1800,   // 30分钟空闲超时
    "persistent-connection": true,     // 启用持久连接
    "tcp-keep-alive": true,           // TCP保活
    "keep-alive-interval": 300,       // 5分钟保活间隔
    "session-sticky": true,           // 会话粘性
    "max-connection-age": 7200,       // 2小时最大存活
    "disable-connection-cleanup": true, // 禁用自动清理
    "connection-pool-size": 16,       // 较小连接池，精细管理
    "http2-ping-interval": 60,        // HTTP/2保活
    "tls-session-reuse": true         // TLS会话复用
  },

  {
    "name": "💻 Cursor",
    "type": "select",
    "proxies": ["🇸🇬 新加坡-AI专用", "🎯 落地节点", "🏠 家宽"],
    // Cursor专用配置（开发工具需要更稳定）
    "connection-idle-timeout": 2400,   // 40分钟空闲超时，开发会话更长
    "persistent-connection": true,     // 必须启用持久连接
    "tcp-keep-alive": true,
    "keep-alive-interval": 180,       // 3分钟保活，更频繁
    "session-sticky": true,
    "max-connection-age": 10800,      // 3小时最大存活，支持长时间开发
    "connection-reuse-limit": 1000,   // 高连接复用限制
    "http2-max-concurrent-streams": 200, // 支持更多并发请求
    "priority": "high"                // 高优先级连接
  },

  // 普通网站组 - 快速连接配置
  {
    "name": "📢 Google",
    "type": "url-test",
    "tolerance": 50,
    "interval": 180,
    "timeout": 3000,
    "proxies": ["🇺🇸 美国-通用", "🇭🇰 香港", "🌍 欧洲"],
    // 普通网站快速配置
    "connection-idle-timeout": 300,    // 5分钟空闲超时，快速释放
    "persistent-connection": false,    // 禁用持久连接
    "tcp-keep-alive": true,
    "keep-alive-interval": 60,        // 1分钟保活
    "auto-close-idle-connections": true, // 自动关闭空闲连接
    "max-connection-age": 1800,       // 30分钟最大存活
    "connection-pool-size": 32,       // 较大连接池，支持并发
    "tcp-fast-open": true,            // TCP快速打开
    "tcp-no-delay": true,             // 禁用Nagle算法
    "priority": "speed"               // 速度优先
  }
];
```

#### 5. DNS缓存与连接协同优化 🌐
```javascript
// DNS配置 - 与连接管理协同
const dnsConfig = {
  // 基础DNS配置
  "cache-ttl": 900,                   // 15分钟缓存TTL，平衡性能和时效性
  "fake-ip-cache-size": 32768,        // 32K缓存大小，适中
  "cache-algorithm": "lru",           // LRU算法，自动淘汰

  // 连接相关DNS优化
  "dns-over-https-timeout": 10,       // DoH超时10秒
  "dns-connection-reuse": true,       // DNS连接复用
  "dns-keep-alive": true,             // DNS连接保活
  "dns-connection-pool": 8,           // DNS连接池大小
  "dns-idle-timeout": 300,            // DNS连接空闲超时5分钟

  // AI服务DNS特殊处理
  "ai-services-dns": {
    "cache-ttl": 1800,                // AI服务DNS缓存30分钟，更稳定
    "prefetch": true,                 // 预取AI服务域名
    "sticky-session": true,           // DNS结果会话粘性
    "domains": [
      "openai.com", "api.openai.com",
      "claude.ai", "api.claude.ai",
      "cursor.sh", "api.cursor.sh"
    ]
  },

  // 普通网站DNS快速处理
  "general-sites-dns": {
    "cache-ttl": 600,                 // 普通网站DNS缓存10分钟，更快更新
    "concurrent-queries": 3,          // 并发DNS查询
    "fast-fallback": true,            // 快速回退到备用DNS
    "domains": [
      "google.com", "googleapis.com",
      "youtube.com", "github.com"
    ]
  }
}
```

#### 3. AI服务稳定性配置 🤖
**关键：AI代理组必须使用固定节点**

```javascript
// AI服务专用配置 - 防风控
{
  "name": "🤖 OpenAI",
  "type": "select",                    // 手动选择，绝不自动切换
  "proxies": ["🎯 落地节点", "� 家宽", "🇺� 美国"],
  "include-all": false,
  "disable-udp": false,
  "persistent": true                   // AI服务保持连接持久性
}

{
  "name": "🧠 Claude",
  "type": "select",                    // 手动选择，IP地址稳定
  "proxies": ["🎯 落地节点", "� 家宽", "�🇸 美国"],
  "include-all": false,
  "session-sticky": true               // 会话粘性，同一会话使用同一节点
}

{
  "name": "💻 Cursor",
  "type": "select",                    // 手动选择，避免开发中断
  "proxies": ["🇸🇬 新加坡", "� 落地节点", "�🇸 美国"],
  "include-all": false,
  "connection-stable": true            // 连接稳定性优先
}
```

#### 4. 路由冲突解决方案 🔀
**核心问题：AI服务固定节点与普通网站速度优化的冲突**

**解决策略：精细化路由分离**

```javascript
// 方案A：AI专用节点池（推荐）
{
  "name": "🤖 OpenAI",
  "type": "select",
  "proxies": [
    "🎯 落地节点",           // AI专用：美国落地，稳定性最佳
    "🏠 家宽",               // AI专用：家宽线路，长期稳定
    "🇺🇸 美国-AI专用"       // AI专用：美国节点，避免与普通网站冲突
  ]
}

{
  "name": "💻 Cursor",
  "type": "select",
  "proxies": [
    "🇸🇬 新加坡-AI专用",    // AI专用：新加坡节点，避免冲突
    "🎯 落地节点",           // 备用选择
    "🏠 家宽"                // 稳定备选
  ]
}

// 方案B：普通网站排除AI节点
{
  "name": "📢 Google",
  "type": "url-test",
  "tolerance": 50,
  "interval": 180,
  "timeout": 3000,
  "proxies": [
    "🇺🇸 美国-通用",        // 排除AI专用的美国节点
    "🇭🇰 香港",             // 速度优选
    "🌍 欧洲",               // 备用选择
    "🇸🇬 新加坡-通用"       // 排除AI专用的新加坡节点
  ],
  "exclude-filter": "(?i)(ai专用|ai-only|cursor|openai)"  // 排除AI专用节点
}
```

#### 5. 节点池分离策略 🎯
**在现有Script.js中实现节点分离：**

```javascript
// 修改区域代理组，创建AI专用和通用分离
const regionalGroupData = [
  // AI专用节点组
  {
    name: "🇺🇸 美国-AI专用",
    type: "select",  // AI专用必须手动选择
    filter: "美国|US|🇺🇸",
    "exclude-filter": "(?i)(游戏|game|netflix|流媒体)",
    tolerance: 100,
    interval: 1800,  // 30分钟检查，减少干扰
    hidden: false
  },

  // 通用高速节点组
  {
    name: "🇺🇸 美国-通用",
    type: "url-test",  // 通用可以自动选择
    filter: "美国|US|🇺🇸",
    "exclude-filter": "(?i)(ai|openai|claude|cursor)",  // 排除AI专用
    tolerance: 50,
    interval: 300,   // 5分钟检查，保证速度
    hidden: false
  },

  // 新加坡分离
  {
    name: "🇸🇬 新加坡-AI专用",
    type: "select",
    filter: "新加坡|SG|🇸🇬",
    tolerance: 100,
    interval: 1800,
    hidden: false
  },

  {
    name: "🇸🇬 新加坡-通用",
    type: "url-test",
    filter: "新加坡|SG|🇸🇬",
    "exclude-filter": "(?i)(ai|cursor)",
    tolerance: 50,
    interval: 300,
    hidden: false
  }
];
```

#### 6. 智能路由规则优化 📋
```javascript
// 规则优先级调整，确保精确路由
const rules = [
  // AI服务优先规则（使用专用节点）
  "DOMAIN-SUFFIX,openai.com,🤖 OpenAI",           // 使用AI专用节点
  "DOMAIN-SUFFIX,cursor.sh,💻 Cursor",            // 使用AI专用节点
  "DOMAIN-SUFFIX,claude.ai,🧠 Claude",            // 使用AI专用节点

  // Google服务规则（使用通用高速节点）
  "DOMAIN-SUFFIX,google.com,📢 Google",           // 使用通用url-test节点
  "DOMAIN-SUFFIX,googleapis.com,📢 Google",       // 自动选择最快
  "DOMAIN-SUFFIX,googleusercontent.com,📢 Google", // 速度优先

  // 其他规则...
];
```

#### 7. 实际配置示例 📝
**基于您现有Script.js的具体实现：**

```javascript
// 在providerGroupData中添加节点分离标识
const providerGroupData = [
  {
    name: "CC CordCloud-AI专用",
    provider: "cordcloud-ai",
    url: "https://www.ccsub.org/link/f9ZZc2KlgR8rcK3T?clash=1",
    prefix: "CC-AI | ",
    type: "select",              // AI专用必须手动选择
    tolerance: 100,
    interval: 1800,              // 30分钟检查
    filter: "(?i)(美国|新加坡|家宽|落地)",  // 只包含AI友好节点
    hidden: false
  },
  {
    name: "CC CordCloud-通用",
    provider: "cordcloud-general",
    url: "https://www.ccsub.org/link/f9ZZc2KlgR8rcK3T?clash=1",
    prefix: "CC-通用 | ",
    type: "url-test",            // 通用可以自动选择
    tolerance: 50,
    interval: 300,               // 5分钟检查
    "exclude-filter": "(?i)(ai|专用)",  // 排除AI专用标识
    hidden: false
  }
];

// 代理组配置调整
const proxyGroupsConfig = [
  // AI服务组 - 使用专用节点池
  {
    "name": "🤖 OpenAI",
    "type": "select",
    "proxies": [
      "🎯 落地节点",           // 首选：美国落地，最稳定
      "🏠 家宽",               // 备选：家宽线路，长期稳定
      "🇺🇸 美国-AI专用",       // 备选：AI专用美国节点
      "CC CordCloud-AI专用"    // 备选：订阅中的AI专用节点
    ]
  },

  // 普通网站组 - 使用通用高速节点池
  {
    "name": "📢 Google",
    "type": "url-test",          // 自动选择最快
    "tolerance": 50,
    "interval": 180,
    "timeout": 3000,
    "proxies": [
      "🇺🇸 美国-通用",         // 美国通用节点，速度优先
      "🇭🇰 香港",             // 香港节点，延迟低
      "🌍 欧洲",               // 欧洲节点，备选
      "CC CordCloud-通用"      // 订阅通用节点，自动选择
    ]
  }
];
```

#### 8. 健康检查分层优化
```javascript
// 针对不同类型代理组的健康检查策略
const groupBaseOption = {
  // AI专用组 - 稳定性优先
  "ai-dedicated": {
    "interval": 1800,                  // 30分钟检查，减少干扰
    "timeout": 10000,                  // 10秒超时，容忍网络波动
    "max-failed-times": 3,             // 3次失败才切换，避免误切
    "lazy": true,                      // 懒加载，减少资源消耗
    "disable-udp": false,              // 保持UDP可用
    "persistent-connection": true      // AI服务保持连接持久性
  },

  // 通用组 - 速度优先
  "general-fast": {
    "interval": 300,                   // 5分钟检查，及时发现问题
    "timeout": 5000,                   // 5秒超时，快速响应
    "max-failed-times": 1,             // 1次失败即切换，保证速度
    "lazy": false,                     // 主动检查，保证速度
    "tcp-fast-open": true,             // 启用快速打开
    "connection-reuse": true           // 启用连接复用
  }
}
```

## 🔧 系统级优化建议

### Windows 网络优化
配合现有的 `windows_network_optimization.ps1` 脚本，添加以下操作：

1. **定期清理系统 HTTP 缓存**
   ```powershell
   # 每天执行一次
   netsh winhttp reset proxy
   ipconfig /flushdns
   ```

2. **浏览器缓存管理**
   - 设置浏览器每天自动清理缓存
   - 或手动每天清理一次浏览器数据

3. **Clash 服务管理**
   - 建议每 24-48 小时重启一次 Clash 服务
   - 可以设置定时任务自动重启

## 📊 监控和诊断

### 问题识别指标
出现以下情况时，建议重启 Clash：

1. **连接异常指标**
   - Google 搜索加载缓慢或失败
   - 浏览器显示 "连接重置" 或 "HTTP 错误"
   - 其他网站正常但 Google 服务异常

2. **时间指标**
   - Clash 连续运行超过 24 小时
   - 上次重启距今超过 48 小时
   - 系统内存使用率持续上升

3. **网络指标**
   - 代理延迟突然增加
   - 连接成功率下降
   - DNS 解析时间异常

### 日志分析
在 Clash Verge 日志中关注以下信息：
```
[WARNING] connection timeout
[ERROR] dial tcp: connection refused
[INFO] http: proxy error
```

## 🎯 最佳实践

### 日常维护流程

#### 🤖 AI服务专项维护

1. **每日检查（2分钟）**
   - 测试主要AI服务是否正常（OpenAI、Claude、Cursor）
   - 确认AI代理组使用的节点未自动切换
   - 记录当前使用的稳定节点

2. **每周维护（3分钟）**
   - 清理浏览器缓存（避免重启Clash）
   - 检查AI服务账户状态
   - 评估当前节点的AI服务访问质量

3. **每月优化（5分钟）**
   - 测试不同节点的AI服务兼容性
   - 更新AI服务节点优先级
   - 检查是否有新的稳定节点

#### 🌐 普通网络维护

1. **每日检查（1分钟）**
   - 测试 Google 搜索是否正常
   - 检查代理节点延迟
   - 观察 Clash 内存使用情况

2. **每周维护（5分钟）**
   - 重启 Clash 服务一次（注意AI服务节点保持）
   - 清理浏览器缓存
   - 检查订阅更新

3. **每月优化（10分钟）**
   - 运行 Windows 网络优化脚本
   - 检查 Clash 配置更新
   - 评估节点质量和速度

### 故障排除流程

**步骤1：快速诊断**
- 测试其他网站是否正常
- 检查当前使用的代理节点
- 查看 Clash 日志最新信息

**步骤2：基础修复**
- 重启 Clash 服务（不重启内核）
- 清理浏览器缓存
- 切换到备用代理节点

**步骤3：深度修复**
- 运行 `ipconfig /flushdns`
- 重启浏览器
- 检查系统网络设置

**步骤4：系统级修复**
- 运行网络优化脚本
- 重启网络适配器
- 必要时重启系统

## 🚨 注意事项

### 重要提醒
1. **不要频繁重启**
   - 避免每小时重启 Clash
   - 正常情况下 24-48 小时重启一次即可

2. **保持配置稳定**
   - 当前 Script.js 配置已经优化
   - 不建议频繁修改核心参数

3. **监控资源使用**
   - 关注 Clash 内存使用情况
   - 64GB 内存配置下正常使用 1-2GB

### 配置兼容性
- 当前配置与 Windows 11 完全兼容
- 支持 TUN 模式和系统代理模式
- 兼容主流浏览器（Chrome、Edge、Firefox）

## 📈 性能指标

### 正常运行指标
- **代理延迟**：< 200ms（优秀）
- **连接成功率**：> 95%
- **DNS 解析时间**：< 100ms
- **内存使用**：< 2GB（64GB 系统）

### 异常警告指标
- **代理延迟**：> 500ms
- **连接成功率**：< 90%
- **DNS 解析时间**：> 300ms
- **内存使用**：> 4GB

## 🎉 总结

**核心要点：**
1. 长时间运行后的 Google 搜索问题是 HTTP 连接缓存问题，不是配置错误
2. 重启 Clash（不重启内核）是最有效的解决方案
3. 当前 Script.js 配置已经很优秀，无需修改
4. 定期维护比频繁调整配置更重要

**长时间稳定运行策略：**
- 🔄 **避免重启Clash**：优先使用浏览器刷新、DNS清理等非侵入方案
- ⚡ **普通网站快速访问**：短连接+快速释放，url-test自动选择最快节点
- 🔒 **AI服务稳定连接**：长连接+保活机制，select模式手动选择固定节点
- 🏊‍♂️ **分层连接管理**：AI服务使用长连接池，普通网站使用快速连接池
- 🧹 **智能缓存策略**：AI服务DNS缓存30分钟，普通网站10分钟
- 📊 **差异化监控**：AI服务和普通网站使用不同的健康检查和连接策略

**连接保活核心策略：**
- 🤖 **AI服务**：30分钟空闲超时，5分钟保活间隔，2小时最大连接存活
- ⚡ **普通网站**：5分钟空闲超时，1分钟保活间隔，30分钟最大连接存活
- 🔗 **HTTP/2优化**：AI服务启用长连接和会话粘性，普通网站启用快速打开
- 🌐 **TLS会话复用**：AI服务1小时会话缓存，普通网站30分钟缓存

**无重启维护流程：**
1. **每日维护（2分钟）**：测试关键服务，清理浏览器缓存，检查连接池状态
2. **每周维护（5分钟）**：DNS缓存清理，TUN模式重置，连接池健康检查
3. **每月维护（10分钟）**：配置文件重载，系统网络优化，连接策略评估

**AI服务风控预防核心：**
- 🎯 AI代理组永远使用 `select` 模式，启用长连接和会话粘性
- 🔒 选定节点后长期使用，通过连接保活维持稳定关系
- 🚫 绝不使用自动切换，避免IP跳跃和连接中断
- 🛡️ 问题优先用浏览器方案解决，保持底层连接稳定

**设计理念：通过精细化连接管理，让Clash像企业级服务一样稳定运行数周，AI服务享受长连接稳定性，普通网站享受快速连接性能！**

## 🎯 核心优化方案（简化版）

### 🔥 内存管理优化（64GB专用）

#### 1. 基础内存配置
```javascript
// 64GB内存优化配置 - 在现有Script.js中调整
const memoryConfig = {
  // 基础内存设置
  "memory-limit": 4096,                    // 4GB内存限制，保守设置
  "memory-pressure-threshold": 80,         // 80%内存压力阈值
  "gc-interval": 1800,                     // 30分钟垃圾回收间隔
  "aggressive-gc": false,                  // 关闭激进GC，保持性能

  // 缓存内存分配
  "dns-cache-size": 256,                   // 256MB DNS缓存
  "connection-cache-size": 512,            // 512MB连接缓存
  "rule-cache-size": 128,                  // 128MB规则缓存
  "proxy-cache-size": 64,                  // 64MB代理缓存

  // 连接对象管理
  "max-connections": 800,                  // 最大连接数800
  "connection-cleanup-interval": 300,      // 5分钟清理一次空闲连接
  "max-idle-connections": 200,             // 最大空闲连接200个
  "connection-timeout": 1800               // 连接超时30分钟
}
```

#### 2. 内存泄漏预防
```javascript
// 简化的内存泄漏预防
const memoryLeakPrevention = {
  // 对象生命周期管理
  "max-object-age": 3600,                  // 对象最大存活1小时
  "auto-cleanup": true,                    // 自动清理过期对象
  "cleanup-interval": 600,                 // 10分钟清理一次

  // 缓存管理
  "max-cache-entries": 50000,              // 最大缓存条目5万
  "cache-ttl": 1800,                       // 缓存TTL 30分钟
  "lru-cleanup": true                      // LRU清理策略
}
```
```

### 🎯 AI服务专项优化

#### 1. AI服务连接稳定性
```javascript
// AI服务专用连接配置
const aiServiceConfig = {
  // 长连接保持
  "connection-idle-timeout": 2400,         // 40分钟空闲超时（开发场景）
  "persistent-connection": true,           // 启用持久连接
  "session-sticky": true,                  // 会话粘性，固定节点
  "keep-alive-interval": 300,              // 5分钟保活间隔
  "max-connection-age": 7200,              // 2小时最大连接存活

  // HTTP/2优化
  "http2-ping-interval": 60,               // 1分钟HTTP/2 ping保活
  "http2-max-concurrent-streams": 100,     // 100个并发流
  "http2-initial-window-size": 1048576,    // 1MB初始窗口

  // 错误处理
  "retry-on-failure": true,                // 失败时重试
  "max-retries": 3,                        // 最大重试3次
  "retry-delay": 5000,                     // 5秒重试延迟
  "preserve-session": true                 // 保持会话状态
}
```

#### 2. 普通网站快速访问
```javascript
// 普通网站快速连接配置
const generalSiteConfig = {
  // 快速连接
  "connection-idle-timeout": 300,          // 5分钟空闲超时，快速释放
  "persistent-connection": false,          // 禁用持久连接
  "tcp-fast-open": true,                   // 启用TCP快速打开
  "tcp-no-delay": true,                    // 禁用Nagle算法
  "connection-reuse": true,                // 启用连接复用

  // 快速故障转移
  "health-check-interval": 180,            // 3分钟健康检查
  "failure-threshold": 1,                  // 1次失败即切换
  "timeout": 3000,                         // 3秒超时
  "auto-switch": true                      // 自动切换最快节点
}
```

## 🛠️ 实际配置实施

### 📋 在现有Script.js中的具体修改

#### 1. 内存和连接管理增强
```javascript
// 在performanceConfig中添加
const performanceConfig = {
  // 现有配置保持不变...

  // 新增：内存管理
  "memory-limit": 4096,                    // 4GB内存限制
  "gc-interval": 1800,                     // 30分钟GC间隔
  "memory-pressure-threshold": 80,         // 80%内存压力阈值

  // 新增：连接管理
  "max-connections": 800,                  // 最大连接数
  "connection-cleanup-interval": 300,      // 5分钟清理间隔
  "max-idle-connections": 200,             // 最大空闲连接

  // 现有配置继续...
}
```

#### 2. AI服务代理组优化
```javascript
// 修改AI服务代理组配置
{
  "name": "🤖 OpenAI",
  "type": "select",                        // 手动选择，避免自动切换
  "proxies": ["🎯 落地节点", "🏠 家宽", "🇺🇸 美国-AI专用"],
  // 新增AI专用配置
  "connection-idle-timeout": 2400,         // 40分钟空闲超时
  "persistent-connection": true,           // 启用持久连接
  "session-sticky": true,                  // 会话粘性
  "keep-alive-interval": 300,              // 5分钟保活
  "max-retries": 3,                        // 最大重试3次
  "retry-delay": 5000                      // 5秒重试延迟
},

{
  "name": "💻 Cursor",
  "type": "select",
  "proxies": ["🇸🇬 新加坡-AI专用", "🎯 落地节点", "🏠 家宽"],
  // Cursor专用配置（开发工具需要更长连接）
  "connection-idle-timeout": 3600,         // 1小时空闲超时
  "persistent-connection": true,
  "session-sticky": true,
  "keep-alive-interval": 180,              // 3分钟保活，更频繁
  "max-connection-age": 10800              // 3小时最大存活
}
```

#### 3. 普通网站代理组优化
```javascript
// 修改普通网站代理组配置
{
  "name": "📢 Google",
  "type": "url-test",                      // 自动选择最快节点
  "tolerance": 50,
  "interval": 180,                         // 3分钟检查
  "timeout": 3000,                         // 3秒超时
  "proxies": ["🇺🇸 美国-通用", "🇭🇰 香港", "🌍 欧洲"],
  // 新增快速访问配置
  "connection-idle-timeout": 300,          // 5分钟空闲超时
  "persistent-connection": false,          // 禁用持久连接
  "tcp-fast-open": true,                   // 快速打开
  "auto-switch": true,                     // 自动切换
  "failure-threshold": 1                   // 1次失败即切换
}
```

### 🎯 简化实施步骤

#### 步骤1：基础优化（立即实施）
1. **内存管理**：调整内存限制和GC间隔
2. **连接分层**：AI服务长连接，普通网站短连接
3. **节点分离**：创建AI专用和通用节点池

#### 步骤2：稳定性增强（1周内）
1. **保活机制**：配置不同的保活间隔
2. **错误处理**：添加重试和恢复机制
3. **会话粘性**：确保AI服务连接稳定

#### 步骤3：性能优化（持续改进）
1. **监控调整**：根据实际使用情况微调参数
2. **节点优化**：评估和优化节点选择
3. **缓存管理**：定期清理和优化缓存

## 📊 预期效果

### 🎯 核心目标达成
- ✅ **Google快速访问**：3秒内响应，自动选择最快节点
- ✅ **AI服务稳定**：长连接保持，避免频繁重连和风控
- ✅ **长时间运行**：数周无需重启，内存稳定
- ✅ **简单维护**：最小化配置复杂度，易于管理

### 📈 性能提升预期
- **AI服务稳定性**：提升80%（减少连接中断）
- **Google访问速度**：提升40%（自动选择最快节点）
- **内存使用效率**：提升50%（优化缓存和清理）
- **长期运行稳定性**：提升90%（内存泄漏预防）

## 🎉 总结

**核心理念：简单高效，专注核心**
- 🔥 **内存优化**：充分利用64GB内存，防止泄漏
- 🎯 **分层连接**：AI服务稳定长连接，普通网站快速短连接
- 🚀 **节点分离**：避免路由冲突，各取所需
- 🛡️ **简化维护**：最小配置变更，最大效果提升

**您的需求完美实现：Google快速 + AI稳定 + 长时间运行！**