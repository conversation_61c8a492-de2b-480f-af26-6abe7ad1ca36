port: 7890
socks-port: 7891
mixed-port: 7892
lan-allowed-ips:
    - 0.0.0.0/0
    - ::/0
allow-lan: true
bind-address: '*'
mode: rule
unified-delay: true
log-level: warning
ipv6: true
external-controller: 0.0.0.0:9090
external-controller-cors:
    allow-origins:
        - '*'
    allow-private-network: true
external-ui: /root/.config/mihomo/ui
external-ui-url: https://github.com/MetaCubeX/metacubexd/archive/refs/heads/gh-pages.zip
external-ui-name: xd
geo-update-interval: 24
geodata-mode: true
geodata-loader: memconservative
tcp-concurrent: true
find-process-mode: strict
global-client-fingerprint: chrome
global-ua: clash.meta/1.10.0
etag-support: true
rule-providers:
    ai_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/ai_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/ai.txt
    apple_cdn:
        behavior: domain
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/apple_cdn.txt
        type: http
        url: https://ruleset.skk.moe/Clash/domainset/apple_cdn.txt
    apple_cn_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/apple_cn_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/apple_cn.txt
    apple_services:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/apple_services.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/apple_services.txt
    cdn_domainset:
        behavior: domain
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/cdn_domainset.txt
        type: http
        url: https://ruleset.skk.moe/Clash/domainset/cdn.txt
    cdn_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/cdn_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/cdn.txt
    china_ip:
        behavior: ipcidr
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/china_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/china_ip.txt
    china_ip_ipv6:
        behavior: ipcidr
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/china_ipv6.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/china_ip_ipv6.txt
    direct_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/direct_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/direct.txt
    domestic_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/domestic_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/domestic.txt
    domestic_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/domestic_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/domestic.txt
    download_domainset:
        behavior: domain
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/download_domainset.txt
        type: http
        url: https://ruleset.skk.moe/Clash/domainset/download.txt
    download_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/download_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/download.txt
    global_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/global_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/global.txt
    lan_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/lan_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/lan.txt
    lan_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/lan_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/lan.txt
    microsoft_cdn_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/microsoft_cdn_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/microsoft_cdn.txt
    microsoft_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/microsoft_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/microsoft.txt
    neteasemusic_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/neteasemusic_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/neteasemusic.txt
    neteasemusic_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/neteasemusic_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/neteasemusic.txt
    reject_domainset:
        behavior: domain
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/reject_domainset.txt
        type: http
        url: https://ruleset.skk.moe/Clash/domainset/reject.txt
    reject_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/reject_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/reject.txt
    reject_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/reject_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/reject.txt
    reject_non_ip_drop:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/reject_non_ip_drop.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/reject-drop.txt
    reject_non_ip_no_drop:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/reject_non_ip_no_drop.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/reject-no-drop.txt
    sogouinput:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/sogouinput.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/sogouinput.txt
    stream_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/stream_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/stream.txt
    stream_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/stream_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/stream.txt
    telegram_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/telegram_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/telegram.txt
    telegram_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/telegram_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/telegram.txt
proxies:
    - cipher: aes-128-gcm
      name: github.com/Ruk1ng001_3166336530306666
      password: VXPipi29nxMO
      plugin: obfs
      plugin-opts:
        host: 182039459a34f64238143cd31ddebbc914362.taobao.com
        mode: http
      port: 1052
      server: kr2.qiangdong.xyz
      type: ss
    - name: github.com/Ruk1ng001_3562656364373131
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: e5987910-655a-4d1d-aab3-a6fc0ae31304
      port: 35210
      server: flymg.flylink.cyou
      skip-cert-verify: true
      sni: flymg.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3663313432393132
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: af5cd0c6-9df5-4728-984a-0a85ecafb668
      port: 33651
      server: jnd.dport.top
      skip-cert-verify: true
      sni: jnd.dport.top
      tls: false
      type: hysteria2
    - client-fingerprint: chrome
      name: "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_3565343138323138"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - auth: a16d62f6-048d-11f0-af5a-f23c93136cb3
      name: github.com/Ruk1ng001_3463336131343434
      password: a16d62f6-048d-11f0-af5a-f23c93136cb3
      port: 1743
      server: b9c3912b-t07z40-t1l1nh-d6ar.la.shifen.uk
      sni: b9c3912b-t07z40-t1l1nh-d6ar.la.shifen.uk
      type: hysteria2
      udp: true
    - name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3333356432343431"
      password: cf125f42-759d-4112-82c3-014d2ffcb1e7
      port: 1443
      server: **************
      skip-cert-verify: true
      sni: gbr1.587458.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6339646565373938
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 7b2e01b1-4f4c-408a-9ee5-ad340fbba0e4
      port: 20757
      server: nexiaels.688997.xyz
      skip-cert-verify: true
      sni: nexiaels.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6434363765663130
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: e5987910-655a-4d1d-aab3-a6fc0ae31304
      port: 35230
      server: flydg.flylink.cyou
      skip-cert-verify: true
      sni: flydg.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3533323030353139
      network: ws
      port: 80
      server: zoomgov.vipren.biz.id
      tls: false
      type: vless
      udp: true
      uuid: 00830f61-5f66-4157-9efd-c054a470ea58
      ws-opts:
        headers:
            Host: zoomgov.vipren.biz.id
        path: /*************=2053
    - alpn:
        - http/1.1
      name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
      network: ws
      password: 07a3df8f-2a2c-42f8-ad92-65889d90f3bf
      port: 443
      server: *************
      skip-cert-verify: false
      sni: rrrRrRRrT.459.pp.ua
      type: trojan
      udp: true
      ws-opts:
        headers:
            Host: rrrRrRRrT.459.pp.ua
        path: /znQImc22ijDwVOkZfoq
    - alterId: 2
      cipher: auto
      name: github.com/Ruk1ng001_6634303333376339
      network: ws
      port: 30824
      server: v24.heduian.link
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
      ws-opts:
        headers:
            Host: baidu.com
        path: /oooo
    - name: github.com/Ruk1ng001_3466326438663839
      network: ws
      port: 443
      server: f3.askkf.com
      servername: s2.askkf.com
      tls: true
      type: vless
      uuid: 81fae892-37b3-47d5-d047-b44b3395fa38
      ws-opts:
        headers:
            Host: s2.askkf.com
        path: /
    - cipher: chacha20-ietf-poly1305
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_6337616130386238"
      password: t0srmdxrm3xyjnvqz9ewlxb2myq7rjuv
      plugin: obfs
      plugin-opts:
        host: (TG @WangCai2)32cf132:178330
        mode: tls
      port: 2377
      server: *************
      type: ss
    - name: github.com/Ruk1ng001_6634356239386330
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 2712bd76-3dd2-4cfb-a962-65ae95825432
      port: 20757
      server: nexiaels.688997.xyz
      skip-cert-verify: true
      sni: nexiaels.688997.xyz
      tls: false
      type: hysteria2
    - name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_63633939303633"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: /?ed=2560
    - name: github.com/Ruk1ng001_6666386564313730
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e8e34487-b0a6-4992-a4b3-17155b4e8639
      port: 25783
      server: krhub.xg-hub.icu
      skip-cert-verify: true
      sni: krhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6235616432666637
      obfs: salamander
      obfs-password: Telegram-->@Ln2Ray
      password: Telegram-->@Ln2Ray
      port: 45000
      server: gavazn.lnmarketplace.net
      skip-cert-verify: false
      sni: gavazn.55994421.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3833383364323665
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0569c5b3-f400-46c8-b71d-2467382c35b8
      port: 20557
      server: nexiadg.688997.xyz
      skip-cert-verify: true
      sni: nexiadg.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6136663837323463
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 707ea935-924c-4127-8b6d-8c0ccf5eabd1
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
      network: ws
      port: 8880
      server: ************
      tls: false
      type: vless
      udp: true
      uuid: e204a396-5b17-46df-94e3-4373a088735d
      ws-opts:
        headers:
            Host: www.speedtest.net.tax.gov.ir.aparat.com.a.1.jEy.dPdNs.org.
        path: /Telegram@vpnjeyTelegram@vpnjeyTelegram@vpnjey
    - name: "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
      network: ws
      port: 8880
      server: ***************
      servername: VngSuPpLY.IP-DdnS.com
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: VngSuPpLY.IP-DdnS.com
        path: /Etb1L6YUdZFZuTOr?ed=2560
    - name: github.com/Ruk1ng001_6536333635643530
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 63399f11-4b3a-4ccb-9b9c-c9a3145912f9
      port: 33657
      server: rb.dport.top
      skip-cert-verify: true
      sni: rb.dport.top
      tls: false
      type: hysteria2
    - name: "\U0001F7E1\U0001F4BB_github.com/Ruk1ng001_6531623064303132"
      network: ws
      port: 8880
      server: ************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6636333131363533
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0004f8b0-df8e-4f3d-866a-e3547122933d
      port: 20757
      server: nexiaels.688997.xyz
      skip-cert-verify: true
      sni: nexiaels.688997.xyz
      tls: false
      type: hysteria2
    - auth: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      name: github.com/Ruk1ng001_3438306631343031
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      port: 33656
      server: hg.dport.top
      skip-cert-verify: true
      sni: hg.dport.top
      type: hysteria2
      udp: true
    - client-fingerprint: chrome
      flow: xtls-rprx-vision
      name: github.com/Ruk1ng001_6563616339333962
      network: tcp
      port: 443
      reality-opts:
        public-key: mLmBhbVFfNuo2eUgBh6r9-5Koz9mUCn3aSzlR6IejUg
        short-id: e499f276e7bd6420
      server: nl01-vlr01.tcp-reset-club.net
      servername: hls-svod.itunes.apple.com
      tls: true
      type: vless
      udp: true
      uuid: c28dfe26-a1cd-4ebf-b427-7b44f8c2f7e0
    - name: "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
      network: ws
      port: 8880
      server: **************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: yd.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - name: "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3461393963333934"
      network: ws
      port: 8880
      server: *************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832646131376238"
      network: ws
      port: 8880
      server: *************
      servername: VNgSupPlY.IP-DDns.Com
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: vngsupply.ip-ddns.com
        path: /bvxIncLfTcZ5ZVxa?ed=2560
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
      network: ws
      port: 8880
      server: *************
      servername: vngsupply.ip-ddns.com
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: vngsupply.ip-ddns.com
        path: /J5aLQOY1R9ONWYCM?ed=2560
    - name: github.com/Ruk1ng001_3937353231343736
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0569c5b3-f400-46c8-b71d-2467382c35b8
      port: 20457
      server: nexiahl.688997.xyz
      skip-cert-verify: true
      sni: nexiahl.688997.xyz
      tls: false
      type: hysteria2
    - client-fingerprint: chrome
      name: "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6565636133346561"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - alterId: 0
      cipher: auto
      country: "\U0001F1F7\U0001F1FARU"
      h2-opts: {}
      http-opts: {}
      name: "\U0001F7E1_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6261373366613764"
      network: ws
      port: 25018
      server: **************
      skip-cert-verify: true
      type: vmess
      uuid: 750b7dd9-37cb-4fd5-9d37-da6f317ec8aa
      ws-opts:
        path: /
    - client-fingerprint: chrome
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234346334313165"
      network: ws
      port: 8880
      server: *************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - cipher: chacha20-ietf-poly1305
      name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3737623131356232"
      password: t0srmdxrm3xyjnvqz9ewlxb2myq7rjuv
      plugin: obfs
      plugin-opts:
        host: (TG @WangCai2)32cf132:178330
        mode: tls
      port: 2377
      server: *************
      type: ss
    - client-fingerprint: chrome
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3265373135626263"
      network: ws
      port: 8880
      server: **************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3666386164666465
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0b52d6c8-445e-4450-a12c-e35fbd73183a
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6664383662343533
      network: ws
      port: 80
      server: vizzpn.freenet.channel.vizzfrag.ir
      tls: false
      type: vless
      udp: true
      uuid: 2036e2c3-18a5-4eed-9db4-f91a7f02c7d5
      ws-opts:
        headers:
            Host: zoomgov.vipren.biz.id
        path: /**************=443
    - name: github.com/Ruk1ng001_32376632636230
      network: ws
      port: 80
      server: light-presence.oneeat.co
      type: vless
      uuid: 7e74ff43-3a90-48b3-8372-7d92a045c2d4
      ws-opts:
        headers:
            host: light-presence.oneeat.co
        path: /
    - alterId: 64
      cipher: auto
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6362343931636662"
      port: 50112
      server: *************
      skip-cert-verify: true
      tls: false
      type: vmess
      uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    - name: github.com/Ruk1ng001_3562336539356461
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0004f8b0-df8e-4f3d-866a-e3547122933d
      port: 20857
      server: nexiabx.688997.xyz
      skip-cert-verify: true
      sni: nexiabx.688997.xyz
      tls: false
      type: hysteria2
    - auth: nfsn666
      name: github.com/Ruk1ng001_6231636539633035
      password: nfsn666
      port: 8888
      server: hk.nfsn666.gq
      skip-cert-verify: true
      sni: hk.nfsn666.gq
      type: hysteria2
    - name: github.com/Ruk1ng001_3561626166323139
      password: 74d138b6-47ee-4afc-a094-5c86d3e00183
      port: 1080
      server: us-elegang-aws-100.77vpn.com
      skip-cert-verify: true
      sni: us-elegang-aws-100.77vpn.com
      type: trojan
      udp: true
    - cipher: chacha20-ietf-poly1305
      name: github.com/Ruk1ng001_6163333066653934
      password: 74d138b6-47ee-4afc-a094-5c86d3e00183
      port: 1081
      server: hk-aws-171.77vpn.com
      type: ss
      udp: true
    - cipher: aes-256-gcm
      name: "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
      password: 9bfdde71b4c0
      plugin: v2ray-plugin
      plugin-opts:
        host: newroot2v1.dsjsapp.com
        mode: websocket
        mux: true
        path: /cauejypbltqt
        skip-cert-verify: false
        tls: true
      port: 636
      server: ***************
      tfo: false
      type: ss
    - cipher: chacha20-ietf-poly1305
      name: "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_35************"
      password: t0srmdxrm3xyjnvqz9ewlxb2myq7rjuv
      plugin: obfs
      plugin-opts:
        host: (TG @WangCai2)32cf132:178330
        mode: tls
      port: 2377
      server: ************
      type: ss
    - client-fingerprint: chrome
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3365343263363465"
      network: ws
      port: 8880
      server: ************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6330363833363365
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1749cfb9-d214-4537-9f2f-543378dcf14f
      port: 33659
      server: hl.dport.top
      skip-cert-verify: true
      sni: hl.dport.top
      tls: false
      type: hysteria2
    - client-fingerprint: chrome
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3339653965346362"
      network: ws
      port: 8880
      server: *************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3362353661633536
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0b52d6c8-445e-4450-a12c-e35fbd73183a
      port: 25785
      server: ukhub.xg-hub.icu
      skip-cert-verify: true
      sni: ukhub.xg-hub.icu
      tls: false
      type: hysteria2
    - cipher: chacha20-ietf-poly1305
      name: github.com/Ruk1ng001_35613039356235
      password: 74d138b6-47ee-4afc-a094-5c86d3e00183
      port: 1081
      server: us-elegang-aws-100.77vpn.com
      type: ss
      udp: true
    - name: github.com/Ruk1ng001_3431316235386230
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 724a0024-6d29-41b9-a510-7d8bd126a380
      port: 33651
      server: jnd.dport.top
      skip-cert-verify: true
      sni: jnd.dport.top
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3138316430316563"
      network: ws
      port: 8880
      server: *************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3663316466623139"
      network: ws
      port: 8880
      server: ***************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - client-fingerprint: chrome
      name: "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3137303533393364"
      network: ws
      port: 8880
      server: ***************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3164663535313063
      password: test.+
      port: 30448
      server: ctsga.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: github.com/Ruk1ng001_31633031356434
      network: ws
      port: 80
      server: famous-experience.seotoolsforyou.co.uk
      tls: false
      type: vless
      udp: true
      uuid: 7f09e7b4-4680-4653-a2da-307d110b6961
      ws-opts:
        headers:
            Host: famous-experience.seotoolsforyou.co.uk
        path: /
    - alpn:
        - http/1.1
      client-fingerprint: chrome
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
      network: ws
      port: 443
      server: *************
      servername: EDfrT.frEEvPNatm2025.DPdNS.oRG
      skip-cert-verify: false
      tls: true
      type: vless
      udp: true
      uuid: ffcf7ec1-3e09-4821-b3d9-b426a107b73b
      ws-opts:
        headers:
            Host: EDfrT.frEEvPNatm2025.DPdNS.oRG
        path: /O9jlBCbIm3xr1D40NK
    - name: github.com/Ruk1ng001_6437303763303333
      password: test.+
      port: 30443
      server: cuhka.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: github.com/Ruk1ng001_6465373134613231
      password: test.+
      port: 40443
      server: cthka.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: github.com/Ruk1ng001_6431323961656438
      network: ws
      port: 8880
      server: ************
      tls: false
      type: vless
      udp: true
      uuid: 02811288-9ef3-42ef-86db-d9b1399c1a59
      ws-opts:
        headers:
            Host: vghavi32.ip-ddns.com
        path: /7YVyoVNnbaVzgl6D?ed=2560
    - client-fingerprint: chrome
      name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
      network: ws
      port: 8443
      server: ************
      servername: ovhwuxian.pai50288.uk
      tls: true
      type: vless
      udp: true
      uuid: 57ba2ab1-a283-42eb-82ee-dc3561a805b8
      ws-opts:
        headers:
            Host: ovhwuxian.pai50288.uk
        path: /57ba2ab1
    - name: github.com/Ruk1ng001_6231393332623436
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0004f8b0-df8e-4f3d-866a-e3547122933d
      port: 20257
      server: nexiarb.688997.xyz
      skip-cert-verify: true
      sni: nexiarb.688997.xyz
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6466343131636431"
      port: 59003
      server: *************
      skip-cert-verify: true
      tls: false
      type: vmess
      uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    - name: github.com/Ruk1ng001_6366646333613962
      password: test.+
      port: 30443
      server: cmhka.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - client-fingerprint: chrome
      name: "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
      network: ws
      port: 2096
      server: **************
      servername: www.dollardoon.com
      tls: true
      type: vless
      udp: true
      uuid: 5fe4abb7-92e6-4390-a17c-fd887f2f1c93
      ws-opts:
        headers:
            Host: www.dollardoon.com
        path: /@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7?ed=2048
    - name: github.com/Ruk1ng001_3564663964613166
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: a65c5cf2-9681-4ebc-904e-11fee411fe6c
      port: 35230
      server: flydg.flylink.cyou
      skip-cert-verify: true
      sni: flydg.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3733633133343762
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 02dc1ff3-a076-4bc1-8f85-10e64a29b754
      port: 20557
      server: nexiadg.688997.xyz
      skip-cert-verify: true
      sni: nexiadg.688997.xyz
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_6235336334656231
      network: ws
      port: 443
      server: ssssssssssssfffffffgh.2032.pp.ua
      skip-cert-verify: false
      tls: true
      type: vmess
      uuid: 4174b95d-115e-4d39-add6-1f8db95bb860
      ws-opts:
        headers:
            Host: ssssssssssssfffffffgh.2032.pp.ua
        path: /6We3U9Df1WGxgFnoFPw1
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534303235613966"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: /?ed=2560
    - client-fingerprint: chrome
      name: github.com/Ruk1ng001_6439636165306266
      network: ws
      port: 443
      server: ipw.gfdv54cvghhgfhgj-njhgj64.info
      servername: 638892846452855773.hamedan-prx-dnraaao.info
      tls: true
      type: vless
      udp: true
      uuid: eb577431-3b16-4a8f-953b-7bcfb573b025
      ws-opts:
        headers:
            Host: 638892846452855773.hamedan-prx-dnraaao.info
        path: /kskoxmws
    - name: "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3332326331663966"
      network: ws
      port: 8880
      server: ************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3236636263303765
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: e45543c2-9fb4-4785-9eea-b202f94dabf3
      port: 35300
      server: flyrb.flylink.cyou
      skip-cert-verify: true
      sni: flyxg.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6564633162393539
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 63399f11-4b3a-4ccb-9b9c-c9a3145912f9
      port: 33653
      server: dg.dport.top
      skip-cert-verify: true
      sni: dg.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_32663463313464
      password: <password>
      port: 443
      server: da63c714-sxaf40-t1bnjq-1krtb.hkt2.cdnhuawei.com
      tls: true
      type: socks5
      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    - alterId: 0
      cipher: auto
      country: "\U0001F1ED\U0001F1F0 HK"
      h2-opts: {}
      http-opts: {}
      name: github.com/Ruk1ng001_3234623938643265
      network: ws
      port: 80
      server: c212722e-szecg0-t0uprz-1th8j.hkt.tcpbbr.net
      servername: broadcastlv.chat.bilibili.com
      skip-cert-verify: true
      type: vmess
      uuid: 7d7c111e-fbc1-11ef-ab5f-f23c913c8d2b
      ws-headers:
        HOST: broadcastlv.chat.bilibili.com
      ws-path: /
    - name: "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6531323934636335"
      network: ws
      port: 8880
      server: ************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: /?ed=2560
    - alpn:
        - http/1.1
      name: github.com/Ruk1ng001_3464386330303961
      network: ws
      password: 0f7070cd-c91d-4532-a51f-56da4f0e94be
      port: 443
      server: iiiiop0.444752.xyz
      skip-cert-verify: false
      sni: iiiiop0.444752.xyz
      type: trojan
      udp: true
      ws-opts:
        headers:
            Host: iiiiop0.444752.xyz
        path: /ctHoQlqeZn8pbEUSLppj7jCmY
    - name: "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3334306131373239"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6539636434623761
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: c1d1cb71-0ae4-44c8-920d-7ae95d3e1187
      port: 25782
      server: hkhub.xg-hub.icu
      skip-cert-verify: true
      sni: hkhub.xg-hub.icu
      tls: false
      type: hysteria2
    - auth: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      name: github.com/Ruk1ng001_3863393430613433
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      port: 33657
      server: rb.dport.top
      skip-cert-verify: true
      sni: rb.dport.top
      type: hysteria2
      udp: true
    - alterId: 2
      cipher: auto
      client-fingerprint: chrome
      name: github.com/Ruk1ng001_6139646430646237
      network: ws
      port: 30808
      server: v8.heduian.link
      skip-cert-verify: false
      tfo: false
      tls: false
      type: vmess
      uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
      ws-opts:
        headers:
            Host: baidu.com
        path: /oooo
    - cipher: chacha20-ietf-poly1305
      name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_36336439643765"
      password: t0srmdxrm3xyjnvqz9ewlxb2myq7rjuv
      plugin: obfs
      plugin-opts:
        host: (TG @WangCai2)32cf132:178330
        mode: tls
      port: 2377
      server: *************
      type: ss
    - name: github.com/Ruk1ng001_3563636561396535
      password: test.+
      port: 30448
      server: ctsgb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - client-fingerprint: chrome
      name: "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3332626436666537"
      network: ws
      port: 8880
      server: *************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3530386133386132
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 139fe171-d1a8-4a08-abd5-81660cd9459b
      port: 33658
      server: teq.dport.top
      skip-cert-verify: true
      sni: teq.dport.top
      tls: false
      type: hysteria2
    - client-fingerprint: chrome
      name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3438363833356166"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
      network: ws
      port: 80
      server: ************
      tls: false
      type: vless
      udp: true
      uuid: 2036e2c3-18a5-4eed-9db4-f91a7f02c7d5
      ws-opts:
        headers:
            Host: zoomgov.vipren.biz.id
        path: /**************=443
    - auth: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      name: github.com/Ruk1ng001_3338656239353332
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      port: 33650
      server: mg.dport.top
      skip-cert-verify: true
      sni: mg.dport.top
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_65353737353831
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: a3321309-41bb-4d53-8028-d6d5ede426a8
      port: 35230
      server: flydg.flylink.cyou
      skip-cert-verify: true
      sni: flydg.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_36343363666665
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: c1d1cb71-0ae4-44c8-920d-7ae95d3e1187
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6334643434646264
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: e45543c2-9fb4-4785-9eea-b202f94dabf3
      port: 35240
      server: flyyg.flylink.cyou
      skip-cert-verify: true
      sni: flyyg.flylink.cyou
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
      network: ws
      port: 8880
      server: *************
      servername: VngSuPpLY.IP-DdnS.com
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: VngSuPpLY.IP-DdnS.com
        path: /1ycR2zb3KeELWRha
    - name: "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3438633666303736"
      network: ws
      port: 8880
      server: ***************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: /?ed=2560
    - client-fingerprint: chrome
      name: "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6364616230623465"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6235393862366434
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1749cfb9-d214-4537-9f2f-543378dcf14f
      port: 33651
      server: jnd.dport.top
      skip-cert-verify: true
      sni: jnd.dport.top
      tls: false
      type: hysteria2
    - client-fingerprint: chrome
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_64356134396635"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6430353337376138
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 02dc1ff3-a076-4bc1-8f85-10e64a29b754
      port: 20157
      server: nexiamg.688997.xyz
      skip-cert-verify: true
      sni: nexiamg.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6662376237646330
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 448f52d9-3b9f-44a6-a204-60dcc9428d63
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3235643564386535
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1749cfb9-d214-4537-9f2f-543378dcf14f
      port: 33653
      server: dg.dport.top
      skip-cert-verify: true
      sni: dg.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6332613833396439
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 139fe171-d1a8-4a08-abd5-81660cd9459b
      port: 33659
      server: hl.dport.top
      skip-cert-verify: true
      sni: hl.dport.top
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3438313436343536"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3362383631666366
      password: 55f2afab-bc41-4231-b725-76bef15c3cf7
      port: 16783
      server: lm.kaiqsz.com
      skip-cert-verify: true
      sni: mmbiz15.redapricotcloud.com
      tfo: false
      type: trojan
      udp: true
    - name: github.com/Ruk1ng001_3738303331643362
      password: test.+
      port: 30443
      server: cthkb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: github.com/Ruk1ng001_3937396532366231
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: 91444c14-779d-4974-a68f-95cb7105f2fa
      port: 35260
      server: flyxghy.flylink.cyou
      skip-cert-verify: true
      sni: flyxghy.flylink.cyou
      tls: false
      type: hysteria2
    - cipher: aes-256-gcm
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
      password: 0b191076d18f
      plugin: v2ray-plugin
      plugin-opts:
        host: hk6v1.dsjsapp.com
        mode: websocket
        mux: true
        path: /ugiibpkbca
        skip-cert-verify: false
        tls: true
      port: 636
      server: ***************
      tfo: false
      type: ss
    - name: github.com/Ruk1ng001_3933383531336336
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: c197f9f0-84e1-4ca4-b87e-b290afa95191
      port: 25783
      server: krhub.xg-hub.icu
      skip-cert-verify: true
      sni: krhub.xg-hub.icu
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_3337366466613035"
      port: 50002
      server: **************
      servername: **************
      skip-cert-verify: true
      tls: false
      type: vmess
      uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    - name: "❓_\U0001F1F7\U0001F1FA_\U0001F4B0_github.com/Ruk1ng001_3333396564343966"
      network: ws
      port: 8880
      server: *************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - cipher: aes-256-cfb
      name: "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
      password: f8f7aCzcPKbsF8p3
      port: 989
      server: *************
      type: ss
      udp: true
    - client-fingerprint: firefox
      name: "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
      network: tcp
      port: 34045
      reality-opts:
        public-key: cDaDzPr3PlS3NM8lreHZbdo-Mhqz8vMBzMSkHXhGIUA
        short-id: e8ab71d0
      server: **************
      servername: visit-this-invitation-link-to-join-tg-enkelte-notif.ekt.me
      tls: true
      type: vless
      uuid: d8dd94fd-540e-461d-b5d4-acebef02c22a
    - name: github.com/Ruk1ng001_3136363866356365
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: 343bdaa2-2f89-4bee-80d4-2a797d8d13fe
      port: 35240
      server: flyyg.flylink.cyou
      skip-cert-verify: true
      sni: flyyg.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6136636466633939
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1d6feed2-6d2e-4509-a304-210de50a41d3
      port: 25783
      server: krhub.xg-hub.icu
      skip-cert-verify: true
      sni: krhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3564306633373131
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 2712bd76-3dd2-4cfb-a962-65ae95825432
      port: 20257
      server: nexiarb.688997.xyz
      skip-cert-verify: true
      sni: nexiarb.688997.xyz
      tls: false
      type: hysteria2
    - client-fingerprint: chrome
      name: github.com/Ruk1ng001_3839643063653164
      network: ws
      port: 2096
      server: ipw.gfdv54cvghhgfhgj-njhgj64.info
      servername: 638890562127852879.amsterdam-prx-dnraaal.info
      tls: true
      type: vless
      udp: true
      uuid: 3056458b-913a-4193-b428-cdf22675b3b5
      ws-opts:
        headers:
            Host: 638890562127852879.amsterdam-prx-dnraaal.info
        path: /ydgmopws
    - name: github.com/Ruk1ng001_3263653961396632
      password: test.+
      port: 30443
      server: cuhkb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - auth: 715a2574-9c25-11eb-8673-f23c9164ca5d
      name: github.com/Ruk1ng001_3532323836336562
      password: 715a2574-9c25-11eb-8673-f23c9164ca5d
      port: 8443
      server: 7101398a-sys4g0-t082yc-xu74.hy2.gotochinatown.net
      sni: 7101398a-sys4g0-t082yc-xu74.hy2.gotochinatown.net
      type: hysteria2
      udp: true
    - name: "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_6662316438616339"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3639386130316131
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 7b2e01b1-4f4c-408a-9ee5-ad340fbba0e4
      port: 20657
      server: nexiayg.688997.xyz
      skip-cert-verify: true
      sni: nexiayg.688997.xyz
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
      network: ws
      port: 8880
      server: **************
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: VngSuPpLY.IP-DdnS.com
        path: /Etb1L6YUdZFZuTOr?ed=2560/?@vpnserverrr_@vpnserverrr_vpnserverrr_@vpnserverrr_@vpnserverrr
    - alpn:
        - http/1.1
      client-fingerprint: chrome
      name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
      network: ws
      port: 443
      server: **************
      servername: eeEEEeR.666470.xYZ
      skip-cert-verify: false
      tls: true
      type: vless
      udp: true
      uuid: 662e38ba-8427-4955-94aa-76f5347a0ce8
      ws-opts:
        headers:
            Host: eeEEEeR.666470.xYZ
        path: /6DuxYMYmrGrnGKRtF5UvWyyVQu
    - name: github.com/Ruk1ng001_3938613662353534
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e21fcd4e-1d46-4b16-b8f4-4c9c51d90879
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - alpn:
        - h3
      auth: eb75320e-a6d0-4b34-a6b6-2bcfc96a9099
      name: "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3933376663323433"
      password: eb75320e-a6d0-4b34-a6b6-2bcfc96a9099
      port: 43057
      server: **************
      skip-cert-verify: true
      sni: www.bing.com
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_6132663631643736
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 6e0656ac-c527-4fbd-97a6-0d791eedbda3
      port: 20857
      server: nexiabx.688997.xyz
      skip-cert-verify: true
      sni: nexiabx.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6464323733386635
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 724a0024-6d29-41b9-a510-7d8bd126a380
      port: 33658
      server: teq.dport.top
      skip-cert-verify: true
      sni: teq.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6634363638653334
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: af5cd0c6-9df5-4728-984a-0a85ecafb668
      port: 33656
      server: hg.dport.top
      skip-cert-verify: true
      sni: hg.dport.top
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      ip-version: dual
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_3930363731346639"
      network: tcp
      port: 50002
      server: **************
      tls: false
      type: vmess
      udp: true
      uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
      network: ws
      port: 80
      server: ***************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare+@WangCai2+/?ed=2560"
    - name: "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
      network: ws
      port: 8880
      server: *************
      servername: vngsupply.ip-ddns.com
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: vngsupply.ip-ddns.com
        path: /J5aLQOY1R9ONWYCM
    - name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
      network: ws
      port: 8880
      server: *************
      servername: vngsupply.ip-ddns.com
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: vngsupply.ip-ddns.com
        path: /J5aLQOY1R9ONWYCM
    - alterId: 2
      cipher: auto
      name: github.com/Ruk1ng001_3536663336653435
      network: ws
      port: 30805
      server: v5.heduian.link
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
      ws-opts:
        headers:
            Host: ocbc.com
        path: /oooo
    - alterId: 0
      cipher: auto
      name: "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
      network: ws
      port: 8880
      server: ***************
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: 248be52b-35d9-34cb-9b73-e12b78bc1301
      ws-opts:
        headers:
            Host: TG.WangCai2.s2.db-link02.top
        path: /dabai.in
    - name: github.com/Ruk1ng001_3163666237653361
      password: test.+
      port: 30443
      server: cthka.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_64306261336663
      network: ws
      port: 80
      server: 4b9d07b6-sz8sg0-t0o6k2-6gn6.hkt.tcpbbr.net
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: c12f73f6-e8c7-11ed-9246-f23c9164ca5d
      ws-opts:
        headers:
            Host: broadcastlv.chat.bilibili.com
        path: /
    - name: "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3665393533353837"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - client-fingerprint: chrome
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3761356632326233"
      network: ws
      port: 8880
      server: ************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - client-fingerprint: chrome
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6638313162396361"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - cipher: chacha20-ietf-poly1305
      name: github.com/Ruk1ng001_3462313237373137
      password: ae6e933b-8874-4bf4-a384-0de216ef2bf4
      port: 40030
      server: pr.fastsoonlink.com
      type: ss
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_6637363964663466
      network: ws
      port: 8880
      server: *************
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: 3e1e3e7f-2683-3f36-83b1-1850790295df
      ws-opts:
        headers:
            Host: TG.WangCai2.s2.cn-db.top
        path: "/dabai&Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - client-fingerprint: chrome
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3233396336383361"
      network: ws
      port: 8880
      server: *************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - alterId: 0
      cipher: auto
      country: "\U0001F1FA\U0001F1F8US"
      h2-opts: {}
      http-opts: {}
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3763343961633537"
      network: ws
      port: 443
      server: **************
      servername: farid.1.berozha.ir
      skip-cert-verify: true
      tls: true
      type: vmess
      uuid: de94cc0a-0592-4969-b1fc-97ea8f0ea0b3
      ws-opts:
        headers:
            HOST: farid.1.berozha.ir
        path: /us.kkp.me.eu.org/aa
    - cipher: chacha20-ietf-poly1305
      name: "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3139323165616438"
      password: 74d138b6-47ee-4afc-a094-5c86d3e00183
      port: 10001
      server: *************
      type: ss
      udp: true
    - name: github.com/Ruk1ng001_3835353935393363
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: c19a5f77-460c-4273-aea7-7666fb9a632a
      port: 35250
      server: flyhl.flylink.cyou
      skip-cert-verify: true
      sni: flyhl.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3466663135313065
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: a65c5cf2-9681-4ebc-904e-11fee411fe6c
      port: 35250
      server: flyhl.flylink.cyou
      skip-cert-verify: true
      sni: flyhl.flylink.cyou
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_3763646137393936
      network: ws
      port: 80
      server: 83da64f6-szi1s0-t0y0nd-1mmbp.hkt.tcpbbr.net
      servername: broadcastlv.chat.bilibili.com
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: 607d365e-7ea1-11ee-95e9-f23c913c8d2b
      ws-opts:
        headers:
            Host: 83da64f6-szi1s0-t0y0nd-1mmbp.hkt.tcpbbr.net
        path: /
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_3664613936616336
      network: ws
      port: 80
      server: 3333r567.11890604.xyz
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: f898ffcb-**************-0b66091e8206
      ws-opts:
        headers:
            Host: 3333r567.11890604.xyz
        path: /GnJ3bBxV91uFkYtuzXyJ5XNeH1R1
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
      network: ws
      port: 80
      server: ***************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare+@WangCai2+/?ed=2560"
    - auth: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      name: github.com/Ruk1ng001_6566633964303465
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      port: 33652
      server: fg.dport.top
      skip-cert-verify: true
      sni: fg.dport.top
      type: hysteria2
      udp: true
    - alterId: 0
      cipher: auto
      name: "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_3232336463333361"
      network: ws
      port: 8880
      server: ***************
      type: vmess
      uuid: 248be52b-35d9-34cb-9b73-e12b78bc1301
      ws-opts:
        headers:
            host: TG.WangCai2.s2.db-link02.top
        path: /dabai.in
    - name: github.com/Ruk1ng001_3530303439646561
      password: <password>
      port: 8443
      server: 2608d13f-sx6ps0-t1bnjq-1krtb.hkt.cdnhuawei.com
      tls: true
      type: http
      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    - name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
      password: 203d1d64-3313-11ed-bb74-f23c9164ca5d
      port: 8443
      server: **************
      sni: 0e1462f1-sum4g0-t8ro7t-1ey07.hy2.gotochinatown.net
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6138303631343566
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 7b2e01b1-4f4c-408a-9ee5-ad340fbba0e4
      port: 20457
      server: nexiahl.688997.xyz
      skip-cert-verify: true
      sni: nexiahl.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3431393931626661
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 033932e3-7f3b-4c5c-96d1-49f525991abf
      port: 25786
      server: twhub.xg-hub.icu
      skip-cert-verify: true
      sni: twhub.xg-hub.icu
      tls: false
      type: hysteria2
    - auth: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      name: github.com/Ruk1ng001_6664623833366434
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      port: 33656
      server: hg.dport.top
      skip-cert-verify: true
      sni: hg.dport.top
      type: hysteria2
      udp: true
    - name: "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6230396164353864"
      network: ws
      port: 8880
      server: **************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: yd.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - alterId: 2
      cipher: auto
      ip-version: dual
      name: github.com/Ruk1ng001_3264323232653631
      network: ws
      port: 30835
      server: v35.heduian.link
      type: vmess
      udp: true
      uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
      ws-opts:
        headers:
            host: baidu.com
        path: /oooo
    - name: "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
      network: ws
      port: 8880
      server: **************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - auth: dongtaiwang.com
      name: "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_39386462363063"
      password: dongtaiwang.com
      port: 22222
      server: **************
      skip-cert-verify: true
      sni: www.bing.com
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_3862646366326534
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 8007932c-b77f-4f02-b8c5-d63c19e4d124
      port: 25785
      server: ukhub.xg-hub.icu
      skip-cert-verify: true
      sni: ukhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6639353032613162
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: f1fe7dcd-d1d7-4663-be22-426150f661e8
      port: 35300
      server: flyrb.flylink.cyou
      skip-cert-verify: true
      sni: flyxg.flylink.cyou
      tls: false
      type: hysteria2
    - auth: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      name: github.com/Ruk1ng001_3132646631306265
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      port: 33653
      server: dg.dport.top
      skip-cert-verify: true
      sni: dg.dport.top
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_3232383363316461
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e6c29ae0-f573-4dca-9b02-b4133edd7e38
      port: 25786
      server: twhub.xg-hub.icu
      skip-cert-verify: true
      sni: twhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3964666564393530"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6463306634376232
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: edf85f14-ba97-49e4-990a-a4d873bab1a1
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6136633862646666
      password: <password>
      port: 8443
      server: 9497cfdb-sx4v40-t1bnjq-1krtb.hkt.cdnhuawei.com
      tls: true
      type: http
      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
      network: ws
      port: 80
      server: ************
      servername: blaze-can-118.blazecanada.site
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 3ac2de34-47c5-4dd5-afc0-8fb4b05d4077
      ws-opts:
        headers:
            Host: blaze-can-118.blazecanada.site
        path: /?ed=2560
    - name: github.com/Ruk1ng001_6636653333643238
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: 91444c14-779d-4974-a68f-95cb7105f2fa
      port: 35270
      server: flyels.flylink.cyou
      skip-cert-verify: true
      sni: flyels.flylink.cyou
      tls: false
      type: hysteria2
    - client-fingerprint: chrome
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3764386435366434"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3562653962643961
      password: test.+
      port: 30443
      server: cmhkb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: github.com/Ruk1ng001_3162613432376163
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 8007932c-b77f-4f02-b8c5-d63c19e4d124
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3965643963343036
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: ed2621e5-67ad-40e7-a2ef-61278db1a59b
      port: 25786
      server: twhub.xg-hub.icu
      skip-cert-verify: true
      sni: twhub.xg-hub.icu
      tls: false
      type: hysteria2
    - alpn:
        - http/1.1
      client-fingerprint: randomized
      name: github.com/Ruk1ng001_3539616164386266
      network: ws
      port: 443
      server: kifpool.me
      servername: mdrN.paGes.Dev
      tls: true
      type: vless
      udp: true
      uuid: 02efda19-4437-4d67-ad4f-3ca613dd80b1
      ws-opts:
        headers:
            Host: mdrn.pages.dev
        path: /kuF4zJYa4FBeudGb?ed=2560
    - name: "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3935313436616265"
      network: ws
      port: 8880
      server: **************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - alterId: 64
      cipher: auto
      name: "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_33626633386536"
      port: 50002
      server: **************
      skip-cert-verify: true
      tls: false
      type: vmess
      uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    - name: github.com/Ruk1ng001_3139343834313837
      password: test.+
      port: 30445
      server: ctusa.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: github.com/Ruk1ng001_3835623631666464
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 724a0024-6d29-41b9-a510-7d8bd126a380
      port: 33653
      server: dg.dport.top
      skip-cert-verify: true
      sni: dg.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3562626665316265
      password: <password>
      port: 8443
      server: 5d847e9d-sxaf40-t1bnjq-1krtb.hkt.cdnhuawei.com
      tls: true
      type: http
      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6163336134623236"
      network: ws
      port: 8880
      server: ***************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - alterId: 0
      cipher: auto
      name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
      network: ws
      port: 443
      server: *************
      skip-cert-verify: true
      tfo: false
      tls: true
      type: vmess
      udp: true
      uuid: de94cc0a-0592-4969-b1fc-97ea8f0ea0b3
      ws-opts:
        headers:
            Host: farid.1.berozha.ir
        path: /us.kkp.me.eu.org/aa
    - name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3131613032366237"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3365306661356538
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 2712bd76-3dd2-4cfb-a962-65ae95825432
      port: 20157
      server: nexiamg.688997.xyz
      skip-cert-verify: true
      sni: nexiamg.688997.xyz
      tls: false
      type: hysteria2
    - client-fingerprint: firefox
      name: "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
      network: tcp
      port: 34045
      reality-opts:
        public-key: cDaDzPr3PlS3NM8lreHZbdo-Mhqz8vMBzMSkHXhGIUA
        short-id: e8ab71d0
      server: ************
      servername: visit-this-invitation-link-to-join-tg-enkelte-notif.ekt.me
      tls: true
      type: vless
      udp: true
      uuid: d8dd94fd-540e-461d-b5d4-acebef02c22a
    - auth: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      name: github.com/Ruk1ng001_3666373466643734
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      port: 33654
      server: bx.dport.top
      skip-cert-verify: true
      sni: bx.dport.top
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_3563646362656235
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: c197f9f0-84e1-4ca4-b87e-b290afa95191
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3462373763626437
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 7b2e01b1-4f4c-408a-9ee5-ad340fbba0e4
      port: 20557
      server: nexiadg.688997.xyz
      skip-cert-verify: true
      sni: nexiadg.688997.xyz
      tls: false
      type: hysteria2
    - cipher: chacha20-ietf
      name: "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_6630303766636432"
      password: asd123456
      port: 8388
      server: ***************
      type: ss
    - alterId: 34463
      cipher: auto
      name: github.com/Ruk1ng001_6538346433393031
      port: 21583
      server: c2nnc-g01.jp03-7d22-vm0.entry.fr0307a.art
      skip-cert-verify: true
      tls: true
      type: vmess
      uuid: 8e555d33-4ce4-395e-93cd-670297aeeb0f
    - name: github.com/Ruk1ng001_3139396234313438
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: d4cf6e04-6614-4e31-bd24-10db576815d9
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3161353234633037
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: cb36d691-0349-4d9a-a040-1e0b3dfb5c12
      port: 35230
      server: flydg.flylink.cyou
      skip-cert-verify: true
      sni: flydg.flylink.cyou
      tls: false
      type: hysteria2
    - name: "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
      obfs: salamander
      obfs-password: Mzc5NTA3MjVlZDMyYTYyMA==
      password: d84be3ca-d0b6-4bd0-9b61-db86d7c65969
      port: 443
      server: *************
      skip-cert-verify: true
      sni: aparat.com
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1F0\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3634383734346538"
      network: ws
      port: 8880
      server: *************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
      network: ws
      port: 8880
      server: ***************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
      ws-opts:
        headers:
            Host: cf.d3z.net
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
      network: ws
      port: 2082
      server: ************
      tls: false
      type: vless
      udp: true
      uuid: 55445977-b31b-4142-83e0-e54b80c14059
      ws-opts:
        headers:
            Host: mrdnzdtnbkwbdfsqoncu.yaSharTeam.com.
        path: /?ed=2052
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_3433393632646435
      network: ws
      port: 80
      server: 711e213a-t09ts0-t0grjj-ezjz.hkt.tcpbbr.net
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: 591ac730-bd6f-11ed-a8bf-f23c91cfbbc9
      ws-opts:
        headers:
            Host: broadcastlv.chat.bilibili.com
        path: /
    - name: github.com/Ruk1ng001_3736626233366431
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 7b2e01b1-4f4c-408a-9ee5-ad340fbba0e4
      port: 20257
      server: nexiarb.688997.xyz
      skip-cert-verify: true
      sni: nexiarb.688997.xyz
      tls: false
      type: hysteria2
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635363061343766"
      network: ws
      port: 8880
      server: *************
      tls: false
      type: vless
      udp: true
      uuid: 02811288-9ef3-42ef-86db-d9b1399c1a59
      ws-opts:
        headers:
            Host: vghavi32.ip-ddns.com
        path: /7YVyoVNnbaVzgl6D?ed=2560
    - alpn:
        - http/1.1
      client-fingerprint: chrome
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
      network: ws
      port: 443
      server: ************
      servername: SssXzAw.444652.XYZ
      tls: true
      type: vless
      uuid: 0f7070cd-c91d-4532-a51f-56da4f0e94be
      ws-opts:
        headers:
            Host: SssXzAw.444652.XYZ
        path: /nSABZLQbEUSLppj7jCmY
    - name: github.com/Ruk1ng001_3565393236356239
      password: test.+
      port: 40443
      server: cuhkb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - auth: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      name: github.com/Ruk1ng001_3137323838373536
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      port: 33658
      server: teq.dport.top
      skip-cert-verify: true
      sni: teq.dport.top
      type: hysteria2
      udp: true
    - cipher: aes-256-gcm
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
      password: 0b191076d18f
      plugin: v2ray-plugin
      plugin-opts:
        host: hk6v1.dsjsapp.com
        mode: websocket
        mux: true
        path: /ugiibpkbca
        skip-cert-verify: false
        tls: true
      port: 636
      server: ***************
      tfo: false
      type: ss
    - name: github.com/Ruk1ng001_31363266336135
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 02dc1ff3-a076-4bc1-8f85-10e64a29b754
      port: 20257
      server: nexiarb.688997.xyz
      skip-cert-verify: true
      sni: nexiarb.688997.xyz
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: chacha20-ietf-poly1305
      name: "\U0001F7E0_\U0001F1EB\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3463343735646631"
      password: oVfvPkqJFCjOEbBsbmOtIiAuqgmEFOEf332F26C8lwEwQng9
      port: 31348
      server: ***************
      skip-cert-verify: true
      tls: true
      type: ss
    - client-fingerprint: chrome
      name: "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
      network: tcp
      port: 34045
      reality-opts:
        public-key: cDaDzPr3PlS3NM8lreHZbdo-Mhqz8vMBzMSkHXhGIUA
        short-id: e8ab71d0
      server: *************
      servername: visit-this-invitation-link-to-join-tg-enkelte-notif.ekt.me
      tls: true
      type: vless
      udp: true
      uuid: d8dd94fd-540e-461d-b5d4-acebef02c22a
    - name: github.com/Ruk1ng001_3634303766363039
      password: <password>
      port: 443
      server: 46977950-sxc9s0-t1bnjq-1krtb.hkt2.cdnhuawei.com
      tls: true
      type: socks5
      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    - name: "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
      network: ws
      port: 8880
      server: ***********
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: VngSuPpLY.IP-DdnS.com
        path: /Etb1L6YUdZFZuTOr?ed=2560/?JOKERRVPNBIA_CHANEL@JOKERRVPN
    - name: github.com/Ruk1ng001_6538633765366135
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: e45543c2-9fb4-4785-9eea-b202f94dabf3
      port: 35260
      server: flyxghy.flylink.cyou
      skip-cert-verify: true
      sni: flyxghy.flylink.cyou
      tls: false
      type: hysteria2
    - alterId: 2
      cipher: auto
      name: github.com/Ruk1ng001_34303031306332
      network: ws
      port: 80
      server: 4ef4bb29-szan40-t0r7zu-17z95.hk.p5pv.com
      servername: broadcastlv.chat.bilibili.com
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: 0a335fd6-be0b-11ec-8dfa-f23c91cfbbc9
      ws-opts:
        headers:
            Host: 4ef4bb29-szan40-t0r7zu-17z95.hk.p5pv.com
        path: /
    - name: github.com/Ruk1ng001_36303563333936
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: 91444c14-779d-4974-a68f-95cb7105f2fa
      port: 35300
      server: flyrb.flylink.cyou
      skip-cert-verify: true
      sni: flyxg.flylink.cyou
      tls: false
      type: hysteria2
    - auth: 5CBqBh6MeDq6GajcilBiDg==
      name: github.com/Ruk1ng001_6436613836333164
      password: 5CBqBh6MeDq6GajcilBiDg==
      port: 61001
      server: 192-227-152-86.nip.io
      skip-cert-verify: true
      sni: 192-227-152-86.nip.io
      type: hysteria2
    - name: github.com/Ruk1ng001_3734343932623736
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 02dc1ff3-a076-4bc1-8f85-10e64a29b754
      port: 20957
      server: nexiateq.688997.xyz
      skip-cert-verify: true
      sni: nexiateq.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3733333936373361
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: 318847a3-bfcb-4c0c-9c1b-365e69ba4ad1
      port: 35240
      server: flyyg.flylink.cyou
      skip-cert-verify: true
      sni: flyyg.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6363316338353961
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 6e0656ac-c527-4fbd-97a6-0d791eedbda3
      port: 20657
      server: nexiayg.688997.xyz
      skip-cert-verify: true
      sni: nexiayg.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3636623563313461
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0004f8b0-df8e-4f3d-866a-e3547122933d
      port: 20357
      server: nexiahg.688997.xyz
      skip-cert-verify: true
      sni: nexiahg.688997.xyz
      tls: false
      type: hysteria2
    - cipher: chacha20-ietf-poly1305
      name: github.com/Ruk1ng001_3661393766373030
      password: 74d138b6-47ee-4afc-a094-5c86d3e00183
      port: 20001
      server: **************
      type: ss
      udp: true
    - client-fingerprint: chrome
      name: "❓_\U0001F1EA\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3563656464656539"
      network: ws
      port: 8880
      server: ************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3665643435623533
      password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
      port: 8443
      server: 416910ec-syf5s0-szvfpn-laev.hy2.gotochinatown.net
      sni: 416910ec-syf5s0-szvfpn-laev.hy2.gotochinatown.net
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3861656566336337"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: /TelegramU0001F1E8U0001F1F3 @WangCai2 /?ed=2560
    - name: github.com/Ruk1ng001_3639373338336331
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 02dc1ff3-a076-4bc1-8f85-10e64a29b754
      port: 20857
      server: nexiabx.688997.xyz
      skip-cert-verify: true
      sni: nexiabx.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3162626630326566
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 63399f11-4b3a-4ccb-9b9c-c9a3145912f9
      port: 33654
      server: bx.dport.top
      skip-cert-verify: true
      sni: bx.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_37323030643661
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 033932e3-7f3b-4c5c-96d1-49f525991abf
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - client-fingerprint: chrome
      name: "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6636306231303866"
      network: ws
      port: 8880
      server: ************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - alterId: 0
      cipher: auto
      country: "\U0001F3C1ZZ"
      h2-opts: {}
      http-opts: {}
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
      network: tcp
      port: 49597
      server: **************
      skip-cert-verify: true
      type: vmess
      uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    - alpn:
        - h3
      auth: cc83a02b-90ea-403b-9c76-27e4be95f637
      name: "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
      password: cc83a02b-90ea-403b-9c76-27e4be95f637
      port: 50256
      server: ***************
      skip-cert-verify: true
      sni: www.bing.com
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_3934653330313137
      password: <password>
      port: 8443
      server: dd902adc-sxc9s0-t1bnjq-1krtb.hkt.cdnhuawei.com
      tls: true
      type: http
      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
      network: ws
      password: f108e0e2-5f12-42b6-9e67-1b2f073ffb2b
      port: 443
      server: **************
      skip-cert-verify: false
      sni: CCcvfgt6.852224.dpdns.org
      type: trojan
      udp: true
      ws-opts:
        headers:
            Host: CCcvfgt6.852224.dpdns.org
        path: /CA5bMmr2JMum8sDKRwvFCJq
    - name: github.com/Ruk1ng001_3431636465393261
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1fe99fb2-9f40-4c5e-b275-de1d49d51409
      port: 20957
      server: nexiateq.688997.xyz
      skip-cert-verify: true
      sni: nexiateq.688997.xyz
      tls: false
      type: hysteria2
    - cipher: aes-256-gcm
      name: "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
      password: 941cbc4237e0
      plugin: v2ray-plugin
      plugin-opts:
        host: kh36v1.dsjsapp.com
        mode: websocket
        mux: true
        path: /tkzhhylcfuf
        skip-cert-verify: false
        tls: true
      port: 636
      server: ***************
      tfo: false
      type: ss
    - name: github.com/Ruk1ng001_3239646232333836
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1d6feed2-6d2e-4509-a304-210de50a41d3
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3736303961346262
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: af5cd0c6-9df5-4728-984a-0a85ecafb668
      port: 33658
      server: teq.dport.top
      skip-cert-verify: true
      sni: teq.dport.top
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1E7\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6665643062356164"
      network: ws
      port: 8880
      server: *************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - cipher: chacha20-ietf-poly1305
      name: "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3938613930633965"
      password: t0srmdxrm3xyjnvqz9ewlxb2myq7rjuv
      plugin: obfs
      plugin-opts:
        host: (TG @WangCai2)32cf132:178330
        mode: tls
      port: 2377
      server: ************
      type: ss
    - name: github.com/Ruk1ng001_3938356531643832
      network: ws
      port: 8880
      server: join.vizzpn.telegram.for.more.freevpn.vizzfrag.ir
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: VngSuPpLY.IP-DdnS.com
        path: /Etb1L6YUdZFZuTOr?ed=2560
    - name: github.com/Ruk1ng001_6335636439313531
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: e561798e-c51f-45c1-b0f6-036db4540205
      port: 35230
      server: flydg.flylink.cyou
      skip-cert-verify: true
      sni: flydg.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6335313363343366
      network: ws
      port: 8880
      server: ::ffff:**************
      servername: sg.laoyoutiao.link
      skip-cert-verify: true
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: sg.laoyoutiao.link
        path: /Telegram@WangCai2/?ed=2048
    - auth: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      name: github.com/Ruk1ng001_6462653139333766
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      port: 33653
      server: dg.dport.top
      skip-cert-verify: true
      sni: dg.dport.top
      type: hysteria2
      udp: true
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_3631396266633036
      network: ws
      port: 8880
      server: *************
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: 3e1e3e7f-2683-3f36-83b1-1850790295df
      ws-opts:
        headers:
            Host: TG.WangCai2.s2.cn-db.top
        path: "/dabai&Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - client-fingerprint: chrome
      name: "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6630326431336330
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 02dc1ff3-a076-4bc1-8f85-10e64a29b754
      port: 20757
      server: nexiaels.688997.xyz
      skip-cert-verify: true
      sni: nexiaels.688997.xyz
      tls: false
      type: hysteria2
    - alterId: 2
      cipher: auto
      name: github.com/Ruk1ng001_3334356634346233
      network: ws
      port: 30830
      server: v30.heduian.link
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
      ws-opts:
        headers:
            Host: ocbc.com
        path: /oooo
    - cipher: chacha20-ietf-poly1305
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6261346464636137"
      password: 74d138b6-47ee-4afc-a094-5c86d3e00183
      port: 20101
      server: **************
      type: ss
      udp: true
    - client-fingerprint: chrome
      name: "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      tls: false
      type: vless
      udp: true
      uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
      ws-opts:
        headers:
            Host: cf.d3z.net
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_3137653963353266
      network: tcp
      port: 3686
      server: 0916af88-t064g0-t1l1nh-d6ar.77.iwskwai.com
      type: vmess
      uuid: a16d62f6-048d-11f0-af5a-f23c93136cb3
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3333326330366538
      password: test.+
      port: 40443
      server: cmhka.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: github.com/Ruk1ng001_6437306131613231
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0569c5b3-f400-46c8-b71d-2467382c35b8
      port: 20757
      server: nexiaels.688997.xyz
      skip-cert-verify: true
      sni: nexiaels.688997.xyz
      tls: false
      type: hysteria2
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
      network: ws
      password: ffcf7ec1-3e09-4821-b3d9-b426a107b73b
      port: 443
      server: *************
      skip-cert-verify: false
      sni: eEEfGty6.999836.XYz
      type: trojan
      ws-opts:
        headers:
            Host: eEEfGty6.999836.XYz
        path: /XmTzATQPJv9RO3xr1D40NK
    - alterId: 0
      cipher: auto
      ip-version: dual
      name: "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_3137383833356432"
      network: tcp
      port: 50002
      server: **************
      tls: false
      type: vmess
      udp: true
      uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    - client-fingerprint: chrome
      name: "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6635626362326138"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - cipher: chacha20-ietf-poly1305
      name: "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3232626433343733"
      password: t0srmdxrm3xyjnvqz9ewlxb2myq7rjuv
      plugin: obfs
      plugin-opts:
        host: (TG @WangCai2)32cf132:178330
        mode: tls
      port: 2377
      server: **************
      type: ss
    - client-fingerprint: chrome
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233393865653937"
      network: ws
      port: 8880
      server: **************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "❓\U0001F4BB_github.com/Ruk1ng001_6438323161313661"
      network: ws
      port: 8880
      server: ************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3338626463343632
      password: test.+
      port: 30445
      server: cmusa.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - auth: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      name: github.com/Ruk1ng001_6133313834333934
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      port: 33659
      server: hl.dport.top
      skip-cert-verify: true
      sni: hl.dport.top
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_3331373033323837
      password: test.+
      port: 30445
      server: cmusb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - alpn:
        - h2
        - http/1.1
      client-fingerprint: randomized
      name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937633561373936"
      network: ws
      port: 443
      server: ************
      servername: reAl-madrID9248.pagEs.dEv
      skip-cert-verify: false
      tls: true
      type: vless
      udp: true
      uuid: f744c2dc-1359-494c-9808-d81ce7665d58
      ws-opts:
        headers:
            Host: reAl-madrID9248.pagEs.dEv
        path: /LQlBRp7bKt33t0q2?ed=2560
    - name: github.com/Ruk1ng001_3230393564333632
      password: test.+
      port: 30445
      server: ctusb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_6535643933383238
      network: ws
      port: 80
      server: 5a2142ce-szi1s0-t0xtcx-1swry.hkt.tcpbbr.net
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: 8a5d58a0-c50e-11ef-a46e-f23c913c8d2b
      ws-opts:
        headers:
            Host: broadcastlv.chat.bilibili.com
        path: /
    - cipher: aes-256-gcm
      name: "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
      password: 823e135ed103
      plugin: v2ray-plugin
      plugin-opts:
        host: jp7v1.lingjfjkm002.com
        mode: websocket
        mux: true
        path: /dchlbvrbbotc
        skip-cert-verify: false
        tls: true
      port: 636
      server: **************
      tfo: false
      type: ss
    - name: "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
      network: tcp
      port: 443
      server: ************
      servername: cs2.pishima.ir
      tls: true
      type: vless
      uuid: e67025a0-278f-4208-8690-cac092b82306
    - name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
      network: ws
      port: 8880
      server: *************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_31353633376634
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 2712bd76-3dd2-4cfb-a962-65ae95825432
      port: 20057
      server: nexiaxg.688997.xyz
      skip-cert-verify: true
      sni: nexiaxg.688997.xyz
      tls: false
      type: hysteria2
    - cipher: aes-256-gcm
      name: "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
      password: 70a0b8103356
      plugin: v2ray-plugin
      plugin-opts:
        host: jp5v1.lingjfjkm002.com
        mode: websocket
        mux: true
        path: /bezradrqccl
        skip-cert-verify: false
        tls: true
      port: 636
      server: ***************
      tfo: false
      type: ss
    - name: github.com/Ruk1ng001_3235303735313835
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: c197f9f0-84e1-4ca4-b87e-b290afa95191
      port: 25786
      server: twhub.xg-hub.icu
      skip-cert-verify: true
      sni: twhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3465343033313861
      password: exaxgqkKkd0TAMrCxeonWg==
      port: 1443
      server: twmoon1-cdn-route.couldflare-cdn.com
      skip-cert-verify: false
      sni: twmoon1-cdn-route.couldflare-cdn.com
      tls: true
      type: http
      username: mrwdfNTD8M79LCukCieldrqZWqs=
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6533343938633961"
      network: ws
      port: 8880
      server: ***********
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: vngsupply.ip-ddns.com
        path: /G8ZDmBYwOAz3ahmR?ed=2560
    - client-fingerprint: chrome
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3534336530613239"
      network: ws
      port: 8880
      server: ***************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "❓_\U0001F1FA\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_6635633432326663"
      network: ws
      port: 8880
      server: **************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: /?ed=2560
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3635363063323866"
      network: ws
      port: 8880
      server: ************
      servername: us.laoyoutiao.link
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: /Telegram@WangCai2/?ed
    - cipher: chacha20-ietf-poly1305
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3864393162363063"
      password: t0srmdxrm3xyjnvqz9ewlxb2myq7rjuv
      plugin: obfs
      plugin-opts:
        host: (TG @WangCai2)32cf132:178330
        mode: tls
      port: 2377
      server: *************
      type: ss
    - alpn:
        - http/1.1
      client-fingerprint: chrome
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
      network: ws
      port: 443
      server: ***********
      servername: ddDDdDDdDDF.777198.XyZ
      skip-cert-verify: false
      tls: true
      type: vless
      udp: true
      uuid: c226ac5d-65e9-4379-95c3-fb542bc242d8
      ws-opts:
        headers:
            Host: ddDDdDDdDDF.777198.XyZ
        path: /OjdW89Bpg4ykd4O
    - client-fingerprint: chrome
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3238666265643830"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6232626131373637
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 338bb7db-e7e0-4279-bf97-ff21b4c3fc67
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_6166666635613931
      network: ws
      port: 80
      server: 91c85a27-t07z40-t0r7l4-1ruih.hkt.tcpbbr.net
      servername: broadcastlv.chat.bilibili.com
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: 3549abda-3854-11ef-b71e-f23c9164ca5d
      ws-opts:
        headers:
            Host: 91c85a27-t07z40-t0r7l4-1ruih.hkt.tcpbbr.net
        path: /
    - name: github.com/Ruk1ng001_3862386134336164
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: e45543c2-9fb4-4785-9eea-b202f94dabf3
      port: 35230
      server: flydg.flylink.cyou
      skip-cert-verify: true
      sni: flydg.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6238323762663634
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: d4a98364-7b10-4ebd-b79b-7f045d66ff33
      port: 35230
      server: flydg.flylink.cyou
      skip-cert-verify: true
      sni: flydg.flylink.cyou
      tls: false
      type: hysteria2
    - cipher: aes-256-cfb
      name: "\U0001F7E1_\U0001F1EE\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3339303930333736"
      password: amazonskr05
      port: 443
      server: ************
      tfo: false
      type: ss
    - name: github.com/Ruk1ng001_3533643165623038
      password: test.+
      port: 40443
      server: cthkb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - auth: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      name: github.com/Ruk1ng001_3435326431633532
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      port: 33657
      server: rb.dport.top
      skip-cert-verify: true
      sni: rb.dport.top
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_3832326663663061
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: ad877c5e-08c7-4499-b139-da371b031194
      port: 35250
      server: flyhl.flylink.cyou
      skip-cert-verify: true
      sni: flyhl.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3634663933633935
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 2456027e-a471-476c-b5d0-4ac80f9aaf8c
      port: 25786
      server: twhub.xg-hub.icu
      skip-cert-verify: true
      sni: twhub.xg-hub.icu
      tls: false
      type: hysteria2
    - cipher: aes-256-gcm
      name: "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
      password: 823e135ed103
      plugin: v2ray-plugin
      plugin-opts:
        host: jp7v1.lingjfjkm002.com
        mode: websocket
        mux: true
        path: /dchlbvrbbotc
        skip-cert-verify: false
        tls: true
      port: 636
      server: **************
      tfo: false
      type: ss
    - name: github.com/Ruk1ng001_3330343539373730
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 591e50da-9904-467e-94e8-397a35c4f4a6
      port: 25786
      server: twhub.xg-hub.icu
      skip-cert-verify: true
      sni: twhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6666306537316635
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 02dc1ff3-a076-4bc1-8f85-10e64a29b754
      port: 20657
      server: nexiayg.688997.xyz
      skip-cert-verify: true
      sni: nexiayg.688997.xyz
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_6162666636316566"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234396461633933"
      password: dongtaiwang.com
      port: 63015
      server: *************
      skip-cert-verify: true
      sni: apple.com
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6462316532396330
      password: test.+
      port: 40443
      server: cuhka.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: github.com/Ruk1ng001_3134393730333237
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e8e34487-b0a6-4992-a4b3-17155b4e8639
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: "\U0001F7E0_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_3364383665656639"
      network: ws
      port: 8880
      server: ***************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3531366264386430"
      network: ws
      port: 8880
      server: ***************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3733353962346137
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: d3b8fcfb-4f76-476e-9c7d-ce43a1d32267
      port: 20057
      server: nexiaxg.688997.xyz
      skip-cert-verify: true
      sni: nexiaxg.688997.xyz
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3533636666656439"
      network: ws
      port: 8880
      server: ***************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: /TelegramU0001F1E8U0001F1F3 @WangCai2 /?ed=2560
    - cipher: aes-256-gcm
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
      password: 23d03aa704c7
      plugin: v2ray-plugin
      plugin-opts:
        host: cmihk3v1.dsjsapp.com
        mode: websocket
        mux: true
        path: /grqhyifio
        skip-cert-verify: false
        tls: true
      port: 636
      server: ***************
      tfo: false
      type: ss
    - name: github.com/Ruk1ng001_3432613333366433
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0e122b6d-67ea-4243-a7fb-641a544c7bf0
      port: 25786
      server: twhub.xg-hub.icu
      skip-cert-verify: true
      sni: twhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3533383239303138
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: d4cf6e04-6614-4e31-bd24-10db576815d9
      port: 25786
      server: twhub.xg-hub.icu
      skip-cert-verify: true
      sni: twhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6264653634666432
      network: ws
      port: 8880
      server: *************
      tls: false
      type: vless
      udp: true
      uuid: 02811288-9ef3-42ef-86db-d9b1399c1a59
      ws-opts:
        headers:
            Host: vghavi32.ip-ddns.com
        path: /7YVyoVNnbaVzgl6D?ed=2560
    - name: github.com/Ruk1ng001_3237343865636465
      password: 74d138b6-47ee-4afc-a094-5c86d3e00183
      port: 1080
      server: hk-aws-171.77vpn.com
      skip-cert-verify: true
      sni: hk-aws-171.77vpn.com
      type: trojan
      udp: true
    - name: github.com/Ruk1ng001_6239313963373066
      password: <password>
      port: 8443
      server: 0a5eba5f-sx15s0-t1bnjq-1krtb.hkt.cdnhuawei.com
      tls: true
      type: http
      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_39383432633836"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - auth: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      name: github.com/Ruk1ng001_6332626561616562
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      port: 33650
      server: mg.dport.top
      skip-cert-verify: true
      sni: mg.dport.top
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_3238646331663237
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 139fe171-d1a8-4a08-abd5-81660cd9459b
      port: 33651
      server: jnd.dport.top
      skip-cert-verify: true
      sni: jnd.dport.top
      tls: false
      type: hysteria2
proxy-groups:
    - interval: 300
      name: "\U0001F3AF"
      proxies:
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3333356432343431"
        - github.com/Ruk1ng001_6339646565373938
        - github.com/Ruk1ng001_3533323030353139
        - github.com/Ruk1ng001_3466326438663839
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_6337616130386238"
        - github.com/Ruk1ng001_6634356239386330
        - github.com/Ruk1ng001_6235616432666637
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - github.com/Ruk1ng001_6536333635643530
        - github.com/Ruk1ng001_6636333131363533
        - github.com/Ruk1ng001_3438306631343031
        - github.com/Ruk1ng001_6563616339333962
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_3937353231343736
        - "\U0001F7E1_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6261373366613764"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3737623131356232"
        - github.com/Ruk1ng001_32376632636230
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_6163333066653934
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - github.com/Ruk1ng001_6330363833363365
        - github.com/Ruk1ng001_3362353661633536
        - github.com/Ruk1ng001_3431316235386230
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_31633031356434
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_6437303763303333
        - github.com/Ruk1ng001_6465373134613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6231393332623436
        - github.com/Ruk1ng001_6366646333613962
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_3236636263303765
        - github.com/Ruk1ng001_32663463313464
        - github.com/Ruk1ng001_3234623938643265
        - github.com/Ruk1ng001_3464386330303961
        - github.com/Ruk1ng001_6539636434623761
        - github.com/Ruk1ng001_3863393430613433
        - github.com/Ruk1ng001_6139646430646237
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_36336439643765"
        - github.com/Ruk1ng001_3563636561396535
        - github.com/Ruk1ng001_3530386133386132
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - github.com/Ruk1ng001_65353737353831
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - github.com/Ruk1ng001_6430353337376138
        - github.com/Ruk1ng001_6662376237646330
        - github.com/Ruk1ng001_6332613833396439
        - github.com/Ruk1ng001_3362383631666366
        - github.com/Ruk1ng001_3937396532366231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3933383531336336
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - github.com/Ruk1ng001_3136363866356365
        - github.com/Ruk1ng001_6136636466633939
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3639386130316131
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_6132663631643736
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3536663336653435
        - github.com/Ruk1ng001_3163666237653361
        - github.com/Ruk1ng001_64306261336663
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3665393533353837"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3763343961633537"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3139323165616438"
        - github.com/Ruk1ng001_3835353935393363
        - github.com/Ruk1ng001_3763646137393936
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6566633964303465
        - github.com/Ruk1ng001_3530303439646561
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6230396164353864"
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_39386462363063"
        - github.com/Ruk1ng001_3862646366326534
        - github.com/Ruk1ng001_6639353032613162
        - github.com/Ruk1ng001_6136633862646666
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - github.com/Ruk1ng001_6636653333643238
        - github.com/Ruk1ng001_3562653962643961
        - github.com/Ruk1ng001_3139343834313837
        - github.com/Ruk1ng001_3562626665316265
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_6538346433393031
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_3736626233366431
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_3565393236356239
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_31363266336135
        - "\U0001F7E0_\U0001F1EB\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3463343735646631"
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_6538633765366135
        - github.com/Ruk1ng001_34303031306332
        - github.com/Ruk1ng001_3734343932623736
        - github.com/Ruk1ng001_3733333936373361
        - github.com/Ruk1ng001_3639373338336331
        - github.com/Ruk1ng001_3162626630326566
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_3431636465393261
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_3736303961346262
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3938613930633965"
        - github.com/Ruk1ng001_6335636439313531
        - github.com/Ruk1ng001_6335313363343366
        - github.com/Ruk1ng001_6630326431336330
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6261346464636137"
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3137653963353266
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_6437306131613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3232626433343733"
        - github.com/Ruk1ng001_3338626463343632
        - github.com/Ruk1ng001_3331373033323837
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937633561373936"
        - github.com/Ruk1ng001_3230393564333632
        - github.com/Ruk1ng001_6535643933383238
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - github.com/Ruk1ng001_31353633376634
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - github.com/Ruk1ng001_3465343033313861
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6533343938633961"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3635363063323866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3864393162363063"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "\U0001F7E1_\U0001F1EE\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3339303930333736"
        - github.com/Ruk1ng001_3533643165623038
        - github.com/Ruk1ng001_3435326431633532
        - github.com/Ruk1ng001_3832326663663061
        - github.com/Ruk1ng001_3634663933633935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3330343539373730
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234396461633933"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3733353962346137
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - github.com/Ruk1ng001_3432613333366433
        - github.com/Ruk1ng001_3533383239303138
        - github.com/Ruk1ng001_3237343865636465
        - github.com/Ruk1ng001_6332626561616562
        - github.com/Ruk1ng001_3238646331663237
      test-timeout: 5
      tolerance: 50
      type: url-test
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F47B"
      proxies:
        - github.com/Ruk1ng001_3166336530306666
        - github.com/Ruk1ng001_3562656364373131
        - github.com/Ruk1ng001_3663313432393132
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_3565343138323138"
        - github.com/Ruk1ng001_3463336131343434
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3333356432343431"
        - github.com/Ruk1ng001_6339646565373938
        - github.com/Ruk1ng001_6434363765663130
        - github.com/Ruk1ng001_3533323030353139
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_6634303333376339
        - github.com/Ruk1ng001_3466326438663839
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_6337616130386238"
        - github.com/Ruk1ng001_6634356239386330
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_63633939303633"
        - github.com/Ruk1ng001_6666386564313730
        - github.com/Ruk1ng001_6235616432666637
        - github.com/Ruk1ng001_3833383364323665
        - github.com/Ruk1ng001_6136663837323463
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - github.com/Ruk1ng001_6536333635643530
        - "\U0001F7E1\U0001F4BB_github.com/Ruk1ng001_6531623064303132"
        - github.com/Ruk1ng001_6636333131363533
        - github.com/Ruk1ng001_3438306631343031
        - github.com/Ruk1ng001_6563616339333962
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3461393963333934"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832646131376238"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_3937353231343736
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6565636133346561"
        - "\U0001F7E1_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6261373366613764"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234346334313165"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3737623131356232"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3265373135626263"
        - github.com/Ruk1ng001_3666386164666465
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6362343931636662"
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_6231636539633035
        - github.com/Ruk1ng001_3561626166323139
        - github.com/Ruk1ng001_6163333066653934
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_35************"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3365343263363465"
        - github.com/Ruk1ng001_6330363833363365
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3339653965346362"
        - github.com/Ruk1ng001_3362353661633536
        - github.com/Ruk1ng001_35613039356235
        - github.com/Ruk1ng001_3431316235386230
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3138316430316563"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3663316466623139"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3137303533393364"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_31633031356434
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_6437303763303333
        - github.com/Ruk1ng001_6465373134613231
        - github.com/Ruk1ng001_6431323961656438
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6231393332623436
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6466343131636431"
        - github.com/Ruk1ng001_6366646333613962
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_3564663964613166
        - github.com/Ruk1ng001_3733633133343762
        - github.com/Ruk1ng001_6235336334656231
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534303235613966"
        - github.com/Ruk1ng001_6439636165306266
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3332326331663966"
        - github.com/Ruk1ng001_3236636263303765
        - github.com/Ruk1ng001_6564633162393539
        - github.com/Ruk1ng001_32663463313464
        - github.com/Ruk1ng001_3234623938643265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6531323934636335"
        - github.com/Ruk1ng001_3464386330303961
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3334306131373239"
        - github.com/Ruk1ng001_6539636434623761
        - github.com/Ruk1ng001_3863393430613433
        - github.com/Ruk1ng001_6139646430646237
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_36336439643765"
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3332626436666537"
        - github.com/Ruk1ng001_3530386133386132
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3438363833356166"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - github.com/Ruk1ng001_3338656239353332
        - github.com/Ruk1ng001_65353737353831
        - github.com/Ruk1ng001_36343363666665
        - github.com/Ruk1ng001_6334643434646264
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3438633666303736"
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6364616230623465"
        - github.com/Ruk1ng001_6235393862366434
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_64356134396635"
        - github.com/Ruk1ng001_6430353337376138
        - github.com/Ruk1ng001_6662376237646330
        - github.com/Ruk1ng001_3235643564386535
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3438313436343536"
        - github.com/Ruk1ng001_3362383631666366
        - github.com/Ruk1ng001_3738303331643362
        - github.com/Ruk1ng001_3937396532366231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3933383531336336
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_3337366466613035"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4B0_github.com/Ruk1ng001_3333396564343966"
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - github.com/Ruk1ng001_3136363866356365
        - github.com/Ruk1ng001_6136636466633939
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3532323836336562
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_6662316438616339"
        - github.com/Ruk1ng001_3639386130316131
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_3938613662353534
        - "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3933376663323433"
        - github.com/Ruk1ng001_6132663631643736
        - github.com/Ruk1ng001_6464323733386635
        - github.com/Ruk1ng001_6634363638653334
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_3930363731346639"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3536663336653435
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
        - github.com/Ruk1ng001_3163666237653361
        - github.com/Ruk1ng001_64306261336663
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3665393533353837"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3761356632326233"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6638313162396361"
        - github.com/Ruk1ng001_3462313237373137
        - github.com/Ruk1ng001_6637363964663466
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3233396336383361"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3763343961633537"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3139323165616438"
        - github.com/Ruk1ng001_3835353935393363
        - github.com/Ruk1ng001_3466663135313065
        - github.com/Ruk1ng001_3763646137393936
        - github.com/Ruk1ng001_3664613936616336
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6566633964303465
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_3232336463333361"
        - github.com/Ruk1ng001_3530303439646561
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - github.com/Ruk1ng001_6138303631343566
        - github.com/Ruk1ng001_3431393931626661
        - github.com/Ruk1ng001_6664623833366434
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6230396164353864"
        - github.com/Ruk1ng001_3264323232653631
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_39386462363063"
        - github.com/Ruk1ng001_3862646366326534
        - github.com/Ruk1ng001_6639353032613162
        - github.com/Ruk1ng001_3132646631306265
        - github.com/Ruk1ng001_3232383363316461
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3964666564393530"
        - github.com/Ruk1ng001_6463306634376232
        - github.com/Ruk1ng001_6136633862646666
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - github.com/Ruk1ng001_6636653333643238
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3764386435366434"
        - github.com/Ruk1ng001_3562653962643961
        - github.com/Ruk1ng001_3162613432376163
        - github.com/Ruk1ng001_3965643963343036
        - github.com/Ruk1ng001_3539616164386266
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3935313436616265"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_33626633386536"
        - github.com/Ruk1ng001_3139343834313837
        - github.com/Ruk1ng001_3835623631666464
        - github.com/Ruk1ng001_3562626665316265
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6163336134623236"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3131613032366237"
        - github.com/Ruk1ng001_3365306661356538
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_3563646362656235
        - github.com/Ruk1ng001_3462373763626437
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_6630303766636432"
        - github.com/Ruk1ng001_6538346433393031
        - github.com/Ruk1ng001_3139396234313438
        - github.com/Ruk1ng001_3161353234633037
        - "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
        - "❓_\U0001F1F0\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3634383734346538"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_3736626233366431
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635363061343766"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_3565393236356239
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_31363266336135
        - "\U0001F7E0_\U0001F1EB\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3463343735646631"
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_3634303766363039
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_6538633765366135
        - github.com/Ruk1ng001_34303031306332
        - github.com/Ruk1ng001_36303563333936
        - github.com/Ruk1ng001_6436613836333164
        - github.com/Ruk1ng001_3734343932623736
        - github.com/Ruk1ng001_3733333936373361
        - github.com/Ruk1ng001_6363316338353961
        - github.com/Ruk1ng001_3636623563313461
        - github.com/Ruk1ng001_3661393766373030
        - "❓_\U0001F1EA\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3563656464656539"
        - github.com/Ruk1ng001_3665643435623533
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3861656566336337"
        - github.com/Ruk1ng001_3639373338336331
        - github.com/Ruk1ng001_3162626630326566
        - github.com/Ruk1ng001_37323030643661
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6636306231303866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_3431636465393261
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_3239646232333836
        - github.com/Ruk1ng001_3736303961346262
        - "❓_\U0001F1E7\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6665643062356164"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3938613930633965"
        - github.com/Ruk1ng001_3938356531643832
        - github.com/Ruk1ng001_6335636439313531
        - github.com/Ruk1ng001_6335313363343366
        - github.com/Ruk1ng001_6462653139333766
        - github.com/Ruk1ng001_3631396266633036
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - github.com/Ruk1ng001_6630326431336330
        - github.com/Ruk1ng001_3334356634346233
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6261346464636137"
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3137653963353266
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_6437306131613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_3137383833356432"
        - "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6635626362326138"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3232626433343733"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233393865653937"
        - "❓\U0001F4BB_github.com/Ruk1ng001_6438323161313661"
        - github.com/Ruk1ng001_3338626463343632
        - github.com/Ruk1ng001_6133313834333934
        - github.com/Ruk1ng001_3331373033323837
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937633561373936"
        - github.com/Ruk1ng001_3230393564333632
        - github.com/Ruk1ng001_6535643933383238
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - github.com/Ruk1ng001_31353633376634
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - github.com/Ruk1ng001_3235303735313835
        - github.com/Ruk1ng001_3465343033313861
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6533343938633961"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3534336530613239"
        - "❓_\U0001F1FA\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_6635633432326663"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3635363063323866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3864393162363063"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3238666265643830"
        - github.com/Ruk1ng001_6232626131373637
        - github.com/Ruk1ng001_6166666635613931
        - github.com/Ruk1ng001_3862386134336164
        - github.com/Ruk1ng001_6238323762663634
        - "\U0001F7E1_\U0001F1EE\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3339303930333736"
        - github.com/Ruk1ng001_3533643165623038
        - github.com/Ruk1ng001_3435326431633532
        - github.com/Ruk1ng001_3832326663663061
        - github.com/Ruk1ng001_3634663933633935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3330343539373730
        - github.com/Ruk1ng001_6666306537316635
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_6162666636316566"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234396461633933"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3134393730333237
        - "\U0001F7E0_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_3364383665656639"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3531366264386430"
        - github.com/Ruk1ng001_3733353962346137
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3533636666656439"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - github.com/Ruk1ng001_3432613333366433
        - github.com/Ruk1ng001_3533383239303138
        - github.com/Ruk1ng001_6264653634666432
        - github.com/Ruk1ng001_3237343865636465
        - github.com/Ruk1ng001_6239313963373066
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_39383432633836"
        - github.com/Ruk1ng001_6332626561616562
        - github.com/Ruk1ng001_3238646331663237
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F44B"
      proxies:
        - github.com/Ruk1ng001_3166336530306666
        - github.com/Ruk1ng001_3562656364373131
        - github.com/Ruk1ng001_3663313432393132
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_3565343138323138"
        - github.com/Ruk1ng001_3463336131343434
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3333356432343431"
        - github.com/Ruk1ng001_6339646565373938
        - github.com/Ruk1ng001_6434363765663130
        - github.com/Ruk1ng001_3533323030353139
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_6634303333376339
        - github.com/Ruk1ng001_3466326438663839
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_6337616130386238"
        - github.com/Ruk1ng001_6634356239386330
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_63633939303633"
        - github.com/Ruk1ng001_6666386564313730
        - github.com/Ruk1ng001_6235616432666637
        - github.com/Ruk1ng001_3833383364323665
        - github.com/Ruk1ng001_6136663837323463
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - github.com/Ruk1ng001_6536333635643530
        - "\U0001F7E1\U0001F4BB_github.com/Ruk1ng001_6531623064303132"
        - github.com/Ruk1ng001_6636333131363533
        - github.com/Ruk1ng001_3438306631343031
        - github.com/Ruk1ng001_6563616339333962
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3461393963333934"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832646131376238"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_3937353231343736
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6565636133346561"
        - "\U0001F7E1_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6261373366613764"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234346334313165"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3737623131356232"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3265373135626263"
        - github.com/Ruk1ng001_3666386164666465
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6362343931636662"
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_6231636539633035
        - github.com/Ruk1ng001_3561626166323139
        - github.com/Ruk1ng001_6163333066653934
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_35************"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3365343263363465"
        - github.com/Ruk1ng001_6330363833363365
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3339653965346362"
        - github.com/Ruk1ng001_3362353661633536
        - github.com/Ruk1ng001_35613039356235
        - github.com/Ruk1ng001_3431316235386230
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3138316430316563"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3663316466623139"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3137303533393364"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_31633031356434
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_6437303763303333
        - github.com/Ruk1ng001_6465373134613231
        - github.com/Ruk1ng001_6431323961656438
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6231393332623436
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6466343131636431"
        - github.com/Ruk1ng001_6366646333613962
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_3564663964613166
        - github.com/Ruk1ng001_3733633133343762
        - github.com/Ruk1ng001_6235336334656231
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534303235613966"
        - github.com/Ruk1ng001_6439636165306266
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3332326331663966"
        - github.com/Ruk1ng001_3236636263303765
        - github.com/Ruk1ng001_6564633162393539
        - github.com/Ruk1ng001_32663463313464
        - github.com/Ruk1ng001_3234623938643265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6531323934636335"
        - github.com/Ruk1ng001_3464386330303961
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3334306131373239"
        - github.com/Ruk1ng001_6539636434623761
        - github.com/Ruk1ng001_3863393430613433
        - github.com/Ruk1ng001_6139646430646237
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_36336439643765"
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3332626436666537"
        - github.com/Ruk1ng001_3530386133386132
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3438363833356166"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - github.com/Ruk1ng001_3338656239353332
        - github.com/Ruk1ng001_65353737353831
        - github.com/Ruk1ng001_36343363666665
        - github.com/Ruk1ng001_6334643434646264
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3438633666303736"
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6364616230623465"
        - github.com/Ruk1ng001_6235393862366434
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_64356134396635"
        - github.com/Ruk1ng001_6430353337376138
        - github.com/Ruk1ng001_6662376237646330
        - github.com/Ruk1ng001_3235643564386535
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3438313436343536"
        - github.com/Ruk1ng001_3362383631666366
        - github.com/Ruk1ng001_3738303331643362
        - github.com/Ruk1ng001_3937396532366231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3933383531336336
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_3337366466613035"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4B0_github.com/Ruk1ng001_3333396564343966"
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - github.com/Ruk1ng001_3136363866356365
        - github.com/Ruk1ng001_6136636466633939
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3532323836336562
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_6662316438616339"
        - github.com/Ruk1ng001_3639386130316131
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_3938613662353534
        - "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3933376663323433"
        - github.com/Ruk1ng001_6132663631643736
        - github.com/Ruk1ng001_6464323733386635
        - github.com/Ruk1ng001_6634363638653334
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_3930363731346639"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3536663336653435
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
        - github.com/Ruk1ng001_3163666237653361
        - github.com/Ruk1ng001_64306261336663
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3665393533353837"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3761356632326233"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6638313162396361"
        - github.com/Ruk1ng001_3462313237373137
        - github.com/Ruk1ng001_6637363964663466
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3233396336383361"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3763343961633537"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3139323165616438"
        - github.com/Ruk1ng001_3835353935393363
        - github.com/Ruk1ng001_3466663135313065
        - github.com/Ruk1ng001_3763646137393936
        - github.com/Ruk1ng001_3664613936616336
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6566633964303465
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_3232336463333361"
        - github.com/Ruk1ng001_3530303439646561
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - github.com/Ruk1ng001_6138303631343566
        - github.com/Ruk1ng001_3431393931626661
        - github.com/Ruk1ng001_6664623833366434
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6230396164353864"
        - github.com/Ruk1ng001_3264323232653631
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_39386462363063"
        - github.com/Ruk1ng001_3862646366326534
        - github.com/Ruk1ng001_6639353032613162
        - github.com/Ruk1ng001_3132646631306265
        - github.com/Ruk1ng001_3232383363316461
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3964666564393530"
        - github.com/Ruk1ng001_6463306634376232
        - github.com/Ruk1ng001_6136633862646666
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - github.com/Ruk1ng001_6636653333643238
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3764386435366434"
        - github.com/Ruk1ng001_3562653962643961
        - github.com/Ruk1ng001_3162613432376163
        - github.com/Ruk1ng001_3965643963343036
        - github.com/Ruk1ng001_3539616164386266
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3935313436616265"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_33626633386536"
        - github.com/Ruk1ng001_3139343834313837
        - github.com/Ruk1ng001_3835623631666464
        - github.com/Ruk1ng001_3562626665316265
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6163336134623236"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3131613032366237"
        - github.com/Ruk1ng001_3365306661356538
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_3563646362656235
        - github.com/Ruk1ng001_3462373763626437
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_6630303766636432"
        - github.com/Ruk1ng001_6538346433393031
        - github.com/Ruk1ng001_3139396234313438
        - github.com/Ruk1ng001_3161353234633037
        - "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
        - "❓_\U0001F1F0\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3634383734346538"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_3736626233366431
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635363061343766"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_3565393236356239
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_31363266336135
        - "\U0001F7E0_\U0001F1EB\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3463343735646631"
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_3634303766363039
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_6538633765366135
        - github.com/Ruk1ng001_34303031306332
        - github.com/Ruk1ng001_36303563333936
        - github.com/Ruk1ng001_6436613836333164
        - github.com/Ruk1ng001_3734343932623736
        - github.com/Ruk1ng001_3733333936373361
        - github.com/Ruk1ng001_6363316338353961
        - github.com/Ruk1ng001_3636623563313461
        - github.com/Ruk1ng001_3661393766373030
        - "❓_\U0001F1EA\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3563656464656539"
        - github.com/Ruk1ng001_3665643435623533
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3861656566336337"
        - github.com/Ruk1ng001_3639373338336331
        - github.com/Ruk1ng001_3162626630326566
        - github.com/Ruk1ng001_37323030643661
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6636306231303866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_3431636465393261
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_3239646232333836
        - github.com/Ruk1ng001_3736303961346262
        - "❓_\U0001F1E7\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6665643062356164"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3938613930633965"
        - github.com/Ruk1ng001_3938356531643832
        - github.com/Ruk1ng001_6335636439313531
        - github.com/Ruk1ng001_6335313363343366
        - github.com/Ruk1ng001_6462653139333766
        - github.com/Ruk1ng001_3631396266633036
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - github.com/Ruk1ng001_6630326431336330
        - github.com/Ruk1ng001_3334356634346233
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6261346464636137"
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3137653963353266
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_6437306131613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_3137383833356432"
        - "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6635626362326138"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3232626433343733"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233393865653937"
        - "❓\U0001F4BB_github.com/Ruk1ng001_6438323161313661"
        - github.com/Ruk1ng001_3338626463343632
        - github.com/Ruk1ng001_6133313834333934
        - github.com/Ruk1ng001_3331373033323837
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937633561373936"
        - github.com/Ruk1ng001_3230393564333632
        - github.com/Ruk1ng001_6535643933383238
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - github.com/Ruk1ng001_31353633376634
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - github.com/Ruk1ng001_3235303735313835
        - github.com/Ruk1ng001_3465343033313861
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6533343938633961"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3534336530613239"
        - "❓_\U0001F1FA\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_6635633432326663"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3635363063323866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3864393162363063"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3238666265643830"
        - github.com/Ruk1ng001_6232626131373637
        - github.com/Ruk1ng001_6166666635613931
        - github.com/Ruk1ng001_3862386134336164
        - github.com/Ruk1ng001_6238323762663634
        - "\U0001F7E1_\U0001F1EE\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3339303930333736"
        - github.com/Ruk1ng001_3533643165623038
        - github.com/Ruk1ng001_3435326431633532
        - github.com/Ruk1ng001_3832326663663061
        - github.com/Ruk1ng001_3634663933633935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3330343539373730
        - github.com/Ruk1ng001_6666306537316635
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_6162666636316566"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234396461633933"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3134393730333237
        - "\U0001F7E0_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_3364383665656639"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3531366264386430"
        - github.com/Ruk1ng001_3733353962346137
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3533636666656439"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - github.com/Ruk1ng001_3432613333366433
        - github.com/Ruk1ng001_3533383239303138
        - github.com/Ruk1ng001_6264653634666432
        - github.com/Ruk1ng001_3237343865636465
        - github.com/Ruk1ng001_6239313963373066
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_39383432633836"
        - github.com/Ruk1ng001_6332626561616562
        - github.com/Ruk1ng001_3238646331663237
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F680"
      proxies:
        - github.com/Ruk1ng001_3166336530306666
        - github.com/Ruk1ng001_3562656364373131
        - github.com/Ruk1ng001_3663313432393132
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_3565343138323138"
        - github.com/Ruk1ng001_3463336131343434
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3333356432343431"
        - github.com/Ruk1ng001_6339646565373938
        - github.com/Ruk1ng001_6434363765663130
        - github.com/Ruk1ng001_3533323030353139
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_6634303333376339
        - github.com/Ruk1ng001_3466326438663839
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_6337616130386238"
        - github.com/Ruk1ng001_6634356239386330
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_63633939303633"
        - github.com/Ruk1ng001_6666386564313730
        - github.com/Ruk1ng001_6235616432666637
        - github.com/Ruk1ng001_3833383364323665
        - github.com/Ruk1ng001_6136663837323463
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - github.com/Ruk1ng001_6536333635643530
        - "\U0001F7E1\U0001F4BB_github.com/Ruk1ng001_6531623064303132"
        - github.com/Ruk1ng001_6636333131363533
        - github.com/Ruk1ng001_3438306631343031
        - github.com/Ruk1ng001_6563616339333962
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3461393963333934"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832646131376238"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_3937353231343736
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6565636133346561"
        - "\U0001F7E1_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6261373366613764"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234346334313165"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3737623131356232"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3265373135626263"
        - github.com/Ruk1ng001_3666386164666465
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6362343931636662"
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_6231636539633035
        - github.com/Ruk1ng001_3561626166323139
        - github.com/Ruk1ng001_6163333066653934
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_35************"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3365343263363465"
        - github.com/Ruk1ng001_6330363833363365
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3339653965346362"
        - github.com/Ruk1ng001_3362353661633536
        - github.com/Ruk1ng001_35613039356235
        - github.com/Ruk1ng001_3431316235386230
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3138316430316563"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3663316466623139"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3137303533393364"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_31633031356434
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_6437303763303333
        - github.com/Ruk1ng001_6465373134613231
        - github.com/Ruk1ng001_6431323961656438
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6231393332623436
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6466343131636431"
        - github.com/Ruk1ng001_6366646333613962
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_3564663964613166
        - github.com/Ruk1ng001_3733633133343762
        - github.com/Ruk1ng001_6235336334656231
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534303235613966"
        - github.com/Ruk1ng001_6439636165306266
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3332326331663966"
        - github.com/Ruk1ng001_3236636263303765
        - github.com/Ruk1ng001_6564633162393539
        - github.com/Ruk1ng001_32663463313464
        - github.com/Ruk1ng001_3234623938643265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6531323934636335"
        - github.com/Ruk1ng001_3464386330303961
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3334306131373239"
        - github.com/Ruk1ng001_6539636434623761
        - github.com/Ruk1ng001_3863393430613433
        - github.com/Ruk1ng001_6139646430646237
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_36336439643765"
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3332626436666537"
        - github.com/Ruk1ng001_3530386133386132
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3438363833356166"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - github.com/Ruk1ng001_3338656239353332
        - github.com/Ruk1ng001_65353737353831
        - github.com/Ruk1ng001_36343363666665
        - github.com/Ruk1ng001_6334643434646264
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3438633666303736"
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6364616230623465"
        - github.com/Ruk1ng001_6235393862366434
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_64356134396635"
        - github.com/Ruk1ng001_6430353337376138
        - github.com/Ruk1ng001_6662376237646330
        - github.com/Ruk1ng001_3235643564386535
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3438313436343536"
        - github.com/Ruk1ng001_3362383631666366
        - github.com/Ruk1ng001_3738303331643362
        - github.com/Ruk1ng001_3937396532366231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3933383531336336
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_3337366466613035"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4B0_github.com/Ruk1ng001_3333396564343966"
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - github.com/Ruk1ng001_3136363866356365
        - github.com/Ruk1ng001_6136636466633939
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3532323836336562
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_6662316438616339"
        - github.com/Ruk1ng001_3639386130316131
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_3938613662353534
        - "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3933376663323433"
        - github.com/Ruk1ng001_6132663631643736
        - github.com/Ruk1ng001_6464323733386635
        - github.com/Ruk1ng001_6634363638653334
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_3930363731346639"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3536663336653435
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
        - github.com/Ruk1ng001_3163666237653361
        - github.com/Ruk1ng001_64306261336663
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3665393533353837"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3761356632326233"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6638313162396361"
        - github.com/Ruk1ng001_3462313237373137
        - github.com/Ruk1ng001_6637363964663466
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3233396336383361"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3763343961633537"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3139323165616438"
        - github.com/Ruk1ng001_3835353935393363
        - github.com/Ruk1ng001_3466663135313065
        - github.com/Ruk1ng001_3763646137393936
        - github.com/Ruk1ng001_3664613936616336
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6566633964303465
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_3232336463333361"
        - github.com/Ruk1ng001_3530303439646561
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - github.com/Ruk1ng001_6138303631343566
        - github.com/Ruk1ng001_3431393931626661
        - github.com/Ruk1ng001_6664623833366434
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6230396164353864"
        - github.com/Ruk1ng001_3264323232653631
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_39386462363063"
        - github.com/Ruk1ng001_3862646366326534
        - github.com/Ruk1ng001_6639353032613162
        - github.com/Ruk1ng001_3132646631306265
        - github.com/Ruk1ng001_3232383363316461
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3964666564393530"
        - github.com/Ruk1ng001_6463306634376232
        - github.com/Ruk1ng001_6136633862646666
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - github.com/Ruk1ng001_6636653333643238
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3764386435366434"
        - github.com/Ruk1ng001_3562653962643961
        - github.com/Ruk1ng001_3162613432376163
        - github.com/Ruk1ng001_3965643963343036
        - github.com/Ruk1ng001_3539616164386266
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3935313436616265"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_33626633386536"
        - github.com/Ruk1ng001_3139343834313837
        - github.com/Ruk1ng001_3835623631666464
        - github.com/Ruk1ng001_3562626665316265
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6163336134623236"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3131613032366237"
        - github.com/Ruk1ng001_3365306661356538
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_3563646362656235
        - github.com/Ruk1ng001_3462373763626437
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_6630303766636432"
        - github.com/Ruk1ng001_6538346433393031
        - github.com/Ruk1ng001_3139396234313438
        - github.com/Ruk1ng001_3161353234633037
        - "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
        - "❓_\U0001F1F0\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3634383734346538"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_3736626233366431
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635363061343766"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_3565393236356239
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_31363266336135
        - "\U0001F7E0_\U0001F1EB\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3463343735646631"
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_3634303766363039
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_6538633765366135
        - github.com/Ruk1ng001_34303031306332
        - github.com/Ruk1ng001_36303563333936
        - github.com/Ruk1ng001_6436613836333164
        - github.com/Ruk1ng001_3734343932623736
        - github.com/Ruk1ng001_3733333936373361
        - github.com/Ruk1ng001_6363316338353961
        - github.com/Ruk1ng001_3636623563313461
        - github.com/Ruk1ng001_3661393766373030
        - "❓_\U0001F1EA\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3563656464656539"
        - github.com/Ruk1ng001_3665643435623533
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3861656566336337"
        - github.com/Ruk1ng001_3639373338336331
        - github.com/Ruk1ng001_3162626630326566
        - github.com/Ruk1ng001_37323030643661
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6636306231303866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_3431636465393261
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_3239646232333836
        - github.com/Ruk1ng001_3736303961346262
        - "❓_\U0001F1E7\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6665643062356164"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3938613930633965"
        - github.com/Ruk1ng001_3938356531643832
        - github.com/Ruk1ng001_6335636439313531
        - github.com/Ruk1ng001_6335313363343366
        - github.com/Ruk1ng001_6462653139333766
        - github.com/Ruk1ng001_3631396266633036
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - github.com/Ruk1ng001_6630326431336330
        - github.com/Ruk1ng001_3334356634346233
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6261346464636137"
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3137653963353266
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_6437306131613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_3137383833356432"
        - "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6635626362326138"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3232626433343733"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233393865653937"
        - "❓\U0001F4BB_github.com/Ruk1ng001_6438323161313661"
        - github.com/Ruk1ng001_3338626463343632
        - github.com/Ruk1ng001_6133313834333934
        - github.com/Ruk1ng001_3331373033323837
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937633561373936"
        - github.com/Ruk1ng001_3230393564333632
        - github.com/Ruk1ng001_6535643933383238
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - github.com/Ruk1ng001_31353633376634
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - github.com/Ruk1ng001_3235303735313835
        - github.com/Ruk1ng001_3465343033313861
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6533343938633961"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3534336530613239"
        - "❓_\U0001F1FA\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_6635633432326663"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3635363063323866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3864393162363063"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3238666265643830"
        - github.com/Ruk1ng001_6232626131373637
        - github.com/Ruk1ng001_6166666635613931
        - github.com/Ruk1ng001_3862386134336164
        - github.com/Ruk1ng001_6238323762663634
        - "\U0001F7E1_\U0001F1EE\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3339303930333736"
        - github.com/Ruk1ng001_3533643165623038
        - github.com/Ruk1ng001_3435326431633532
        - github.com/Ruk1ng001_3832326663663061
        - github.com/Ruk1ng001_3634663933633935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3330343539373730
        - github.com/Ruk1ng001_6666306537316635
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_6162666636316566"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234396461633933"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3134393730333237
        - "\U0001F7E0_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_3364383665656639"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3531366264386430"
        - github.com/Ruk1ng001_3733353962346137
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3533636666656439"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - github.com/Ruk1ng001_3432613333366433
        - github.com/Ruk1ng001_3533383239303138
        - github.com/Ruk1ng001_6264653634666432
        - github.com/Ruk1ng001_3237343865636465
        - github.com/Ruk1ng001_6239313963373066
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_39383432633836"
        - github.com/Ruk1ng001_6332626561616562
        - github.com/Ruk1ng001_3238646331663237
      test-timeout: 5
      type: load-balance
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0"
      proxies:
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - github.com/Ruk1ng001_6231636539633035
        - github.com/Ruk1ng001_6163333066653934
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - github.com/Ruk1ng001_32663463313464
        - github.com/Ruk1ng001_3234623938643265
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_64306261336663
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3139323165616438"
        - github.com/Ruk1ng001_3763646137393936
        - github.com/Ruk1ng001_3530303439646561
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6230396164353864"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - github.com/Ruk1ng001_6136633862646666
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3935313436616265"
        - github.com/Ruk1ng001_3562626665316265
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_6630303766636432"
        - github.com/Ruk1ng001_3433393632646435
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_3634303766363039
        - github.com/Ruk1ng001_34303031306332
        - github.com/Ruk1ng001_3661393766373030
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6261346464636137"
        - github.com/Ruk1ng001_6535643933383238
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - github.com/Ruk1ng001_6166666635613931
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - github.com/Ruk1ng001_3237343865636465
        - github.com/Ruk1ng001_6239313963373066
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC"
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_6337616130386238"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_35************"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_6437303763303333
        - github.com/Ruk1ng001_6465373134613231
        - github.com/Ruk1ng001_6366646333613962
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3163666237653361
        - github.com/Ruk1ng001_3562653962643961
        - github.com/Ruk1ng001_3139343834313837
        - github.com/Ruk1ng001_3565393236356239
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3938613930633965"
        - github.com/Ruk1ng001_3333326330366538
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3232626433343733"
        - github.com/Ruk1ng001_3465343033313861
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3864393162363063"
        - github.com/Ruk1ng001_6462316532396330
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1FA\U0001F1F2"
      proxies:
        - github.com/Ruk1ng001_3463336131343434
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_3466326438663839
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - "\U0001F7E1\U0001F4BB_github.com/Ruk1ng001_6531623064303132"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832646131376238"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - "\U0001F7E1_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6261373366613764"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3737623131356232"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3265373135626263"
        - github.com/Ruk1ng001_32376632636230
        - github.com/Ruk1ng001_3561626166323139
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3339653965346362"
        - github.com/Ruk1ng001_35613039356235
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3138316430316563"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_6431323961656438
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534303235613966"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_3464386330303961
        - github.com/Ruk1ng001_6139646430646237
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_36336439643765"
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3332626436666537"
        - github.com/Ruk1ng001_3338656239353332
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_64356134396635"
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3532323836336562
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3665393533353837"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3761356632326233"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6638313162396361"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3233396336383361"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3763343961633537"
        - github.com/Ruk1ng001_3664613936616336
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_39386462363063"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3764386435366434"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6163336134623236"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635363061343766"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_6436613836333164
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3861656566336337"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - "❓_\U0001F1E7\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6665643062356164"
        - github.com/Ruk1ng001_3631396266633036
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3137653963353266
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233393865653937"
        - github.com/Ruk1ng001_3331373033323837
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937633561373936"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6533343938633961"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3534336530613239"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3635363063323866"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3238666265643830"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3533636666656439"
        - github.com/Ruk1ng001_6264653634666432
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_39383432633836"
        - github.com/Ruk1ng001_6332626561616562
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F7\U0001F1FA"
      proxies:
        - "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3461393963333934"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6565636133346561"
        - "\U0001F7E1_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6261373366613764"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3137303533393364"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3438633666303736"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4B0_github.com/Ruk1ng001_3333396564343966"
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - github.com/Ruk1ng001_6637363964663466
        - "\U0001F7E0_\U0001F1EB\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3463343735646631"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6636306231303866"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3531366264386430"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EE\U0001F1F3"
      proxies:
        - github.com/Ruk1ng001_6439636165306266
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - github.com/Ruk1ng001_3839643063653164
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E7\U0001F1F7"
      proxies:
        - github.com/Ruk1ng001_6439636165306266
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_3666373466643734
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E6\U0001F1FA"
      proxies:
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3663316466623139"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EA\U0001F1F8"
      proxies:
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832646131376238"
        - github.com/Ruk1ng001_6431323961656438
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3666373466643734
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635363061343766"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6533343938633961"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3635363063323866"
        - github.com/Ruk1ng001_6264653634666432
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E9\U0001F1EA"
      proxies:
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_3132646631306265
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - github.com/Ruk1ng001_3938356531643832
        - github.com/Ruk1ng001_6462653139333766
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EB\U0001F1F7"
      proxies:
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_6439636165306266
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - github.com/Ruk1ng001_6566633964303465
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EF\U0001F1F5"
      proxies:
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_63633939303633"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3365343263363465"
        - github.com/Ruk1ng001_3863393430613433
        - github.com/Ruk1ng001_6538346433393031
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - github.com/Ruk1ng001_3435326431633532
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EE\U0001F1F9"
      proxies:
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EC\U0001F1E7"
      proxies:
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3333356432343431"
        - github.com/Ruk1ng001_32376632636230
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3438363833356166"
        - github.com/Ruk1ng001_3839643063653164
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3131613032366237"
        - "\U0001F7E0_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_3364383665656639"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F0\U0001F1F7"
      proxies:
        - github.com/Ruk1ng001_3166336530306666
        - github.com/Ruk1ng001_3438306631343031
        - github.com/Ruk1ng001_3563636561396535
        - github.com/Ruk1ng001_3738303331643362
        - github.com/Ruk1ng001_6664623833366434
        - github.com/Ruk1ng001_3338626463343632
        - github.com/Ruk1ng001_3230393564333632
        - github.com/Ruk1ng001_3533643165623038
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E8\U0001F1E6"
      proxies:
        - "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3933376663323433"
        - github.com/Ruk1ng001_3264323232653631
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F3\U0001F1F1"
      proxies:
        - github.com/Ruk1ng001_3533323030353139
        - github.com/Ruk1ng001_6563616339333962
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832646131376238"
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_6431323961656438
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6531323934636335"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635363061343766"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_6133313834333934
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6533343938633961"
        - "❓_\U0001F1FA\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_6635633432326663"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3635363063323866"
        - github.com/Ruk1ng001_6264653634666432
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1FF\U0001F1E6"
      proxies:
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F8\U0001F1EC"
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3332326331663966"
        - "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3933376663323433"
        - "❓_\U0001F1F0\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3634383734346538"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EE\U0001F1F7"
      proxies:
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F9\U0001F1F7"
      proxies:
        - github.com/Ruk1ng001_3137323838373536
        - github.com/Ruk1ng001_3431636465393261
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F5\U0001F1F1"
      proxies:
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3462313237373137
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F3\U0001F1F4"
      proxies:
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1FA\U0001F1E6"
      proxies:
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F8\U0001F1EE"
      proxies:
        - github.com/Ruk1ng001_3533323030353139
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - github.com/Ruk1ng001_6563616339333962
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234346334313165"
        - github.com/Ruk1ng001_6664383662343533
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - github.com/Ruk1ng001_6637363964663466
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E0_\U0001F1EB\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3463343735646631"
        - "❓\U0001F4BB_github.com/Ruk1ng001_6438323161313661"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E6\U0001F1F9"
      proxies:
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_3565343138323138"
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_6662316438616339"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E6\U0001F1F7"
      proxies:
        - github.com/Ruk1ng001_3533323030353139
        - github.com/Ruk1ng001_6563616339333962
        - github.com/Ruk1ng001_6664383662343533
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - github.com/Ruk1ng001_3539616164386266
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1FA\U0001F1FE"
      proxies:
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F8\U0001F1F0"
      proxies:
        - github.com/Ruk1ng001_3533323030353139
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - github.com/Ruk1ng001_6563616339333962
        - github.com/Ruk1ng001_6664383662343533
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - github.com/Ruk1ng001_6637363964663466
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E0_\U0001F1EB\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3463343735646631"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F1\U0001F1FA"
      proxies:
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_3839643063653164
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F916"
      proxies:
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3333356432343431"
        - github.com/Ruk1ng001_6339646565373938
        - github.com/Ruk1ng001_3533323030353139
        - github.com/Ruk1ng001_3466326438663839
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_6337616130386238"
        - github.com/Ruk1ng001_6634356239386330
        - github.com/Ruk1ng001_6235616432666637
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - github.com/Ruk1ng001_6536333635643530
        - github.com/Ruk1ng001_6636333131363533
        - github.com/Ruk1ng001_3438306631343031
        - github.com/Ruk1ng001_6563616339333962
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_3937353231343736
        - "\U0001F7E1_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6261373366613764"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3737623131356232"
        - github.com/Ruk1ng001_32376632636230
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_6163333066653934
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - github.com/Ruk1ng001_6330363833363365
        - github.com/Ruk1ng001_3362353661633536
        - github.com/Ruk1ng001_3431316235386230
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_31633031356434
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_6437303763303333
        - github.com/Ruk1ng001_6465373134613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6231393332623436
        - github.com/Ruk1ng001_6366646333613962
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_3236636263303765
        - github.com/Ruk1ng001_32663463313464
        - github.com/Ruk1ng001_3234623938643265
        - github.com/Ruk1ng001_3464386330303961
        - github.com/Ruk1ng001_6539636434623761
        - github.com/Ruk1ng001_3863393430613433
        - github.com/Ruk1ng001_6139646430646237
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_36336439643765"
        - github.com/Ruk1ng001_3563636561396535
        - github.com/Ruk1ng001_3530386133386132
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - github.com/Ruk1ng001_65353737353831
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - github.com/Ruk1ng001_6430353337376138
        - github.com/Ruk1ng001_6662376237646330
        - github.com/Ruk1ng001_6332613833396439
        - github.com/Ruk1ng001_3362383631666366
        - github.com/Ruk1ng001_3937396532366231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3933383531336336
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - github.com/Ruk1ng001_3136363866356365
        - github.com/Ruk1ng001_6136636466633939
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3639386130316131
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_6132663631643736
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3536663336653435
        - github.com/Ruk1ng001_3163666237653361
        - github.com/Ruk1ng001_64306261336663
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3665393533353837"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3763343961633537"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3139323165616438"
        - github.com/Ruk1ng001_3835353935393363
        - github.com/Ruk1ng001_3763646137393936
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6566633964303465
        - github.com/Ruk1ng001_3530303439646561
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6230396164353864"
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_39386462363063"
        - github.com/Ruk1ng001_3862646366326534
        - github.com/Ruk1ng001_6639353032613162
        - github.com/Ruk1ng001_6136633862646666
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - github.com/Ruk1ng001_6636653333643238
        - github.com/Ruk1ng001_3562653962643961
        - github.com/Ruk1ng001_3139343834313837
        - github.com/Ruk1ng001_3562626665316265
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_6538346433393031
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_3736626233366431
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_3565393236356239
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_31363266336135
        - "\U0001F7E0_\U0001F1EB\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3463343735646631"
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_6538633765366135
        - github.com/Ruk1ng001_34303031306332
        - github.com/Ruk1ng001_3734343932623736
        - github.com/Ruk1ng001_3733333936373361
        - github.com/Ruk1ng001_3639373338336331
        - github.com/Ruk1ng001_3162626630326566
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_3431636465393261
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_3736303961346262
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3938613930633965"
        - github.com/Ruk1ng001_6335636439313531
        - github.com/Ruk1ng001_6335313363343366
        - github.com/Ruk1ng001_6630326431336330
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6261346464636137"
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3137653963353266
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_6437306131613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3232626433343733"
        - github.com/Ruk1ng001_3338626463343632
        - github.com/Ruk1ng001_3331373033323837
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937633561373936"
        - github.com/Ruk1ng001_3230393564333632
        - github.com/Ruk1ng001_6535643933383238
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - github.com/Ruk1ng001_31353633376634
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - github.com/Ruk1ng001_3465343033313861
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6533343938633961"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3635363063323866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3864393162363063"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "\U0001F7E1_\U0001F1EE\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3339303930333736"
        - github.com/Ruk1ng001_3533643165623038
        - github.com/Ruk1ng001_3435326431633532
        - github.com/Ruk1ng001_3832326663663061
        - github.com/Ruk1ng001_3634663933633935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3330343539373730
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234396461633933"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3733353962346137
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - github.com/Ruk1ng001_3432613333366433
        - github.com/Ruk1ng001_3533383239303138
        - github.com/Ruk1ng001_3237343865636465
        - github.com/Ruk1ng001_6332626561616562
        - github.com/Ruk1ng001_3238646331663237
      test-timeout: 5
      tolerance: 50
      type: url-test
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F4FA"
      proxies:
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_3565343138323138"
        - github.com/Ruk1ng001_3463336131343434
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3333356432343431"
        - github.com/Ruk1ng001_6339646565373938
        - github.com/Ruk1ng001_3533323030353139
        - github.com/Ruk1ng001_3466326438663839
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_6337616130386238"
        - github.com/Ruk1ng001_6634356239386330
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_63633939303633"
        - github.com/Ruk1ng001_6235616432666637
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - github.com/Ruk1ng001_6536333635643530
        - "\U0001F7E1\U0001F4BB_github.com/Ruk1ng001_6531623064303132"
        - github.com/Ruk1ng001_6636333131363533
        - github.com/Ruk1ng001_3438306631343031
        - github.com/Ruk1ng001_6563616339333962
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3461393963333934"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832646131376238"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_3937353231343736
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6565636133346561"
        - "\U0001F7E1_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6261373366613764"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234346334313165"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3737623131356232"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3265373135626263"
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6362343931636662"
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_6163333066653934
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_35************"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3365343263363465"
        - github.com/Ruk1ng001_6330363833363365
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3339653965346362"
        - github.com/Ruk1ng001_3362353661633536
        - github.com/Ruk1ng001_3431316235386230
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3138316430316563"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3663316466623139"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3137303533393364"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_31633031356434
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_6437303763303333
        - github.com/Ruk1ng001_6465373134613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6231393332623436
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6466343131636431"
        - github.com/Ruk1ng001_6366646333613962
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534303235613966"
        - github.com/Ruk1ng001_6439636165306266
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3332326331663966"
        - github.com/Ruk1ng001_3236636263303765
        - github.com/Ruk1ng001_32663463313464
        - github.com/Ruk1ng001_3234623938643265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6531323934636335"
        - github.com/Ruk1ng001_3464386330303961
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3334306131373239"
        - github.com/Ruk1ng001_6539636434623761
        - github.com/Ruk1ng001_3863393430613433
        - github.com/Ruk1ng001_6139646430646237
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_36336439643765"
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3332626436666537"
        - github.com/Ruk1ng001_3530386133386132
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3438363833356166"
        - github.com/Ruk1ng001_65353737353831
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3438633666303736"
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6364616230623465"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_64356134396635"
        - github.com/Ruk1ng001_6430353337376138
        - github.com/Ruk1ng001_6662376237646330
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3438313436343536"
        - github.com/Ruk1ng001_3362383631666366
        - github.com/Ruk1ng001_3738303331643362
        - github.com/Ruk1ng001_3937396532366231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3933383531336336
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_3337366466613035"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4B0_github.com/Ruk1ng001_3333396564343966"
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - github.com/Ruk1ng001_3136363866356365
        - github.com/Ruk1ng001_6136636466633939
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3532323836336562
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_6662316438616339"
        - github.com/Ruk1ng001_3639386130316131
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_3938613662353534
        - github.com/Ruk1ng001_6132663631643736
        - github.com/Ruk1ng001_6464323733386635
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_3930363731346639"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3536663336653435
        - github.com/Ruk1ng001_3163666237653361
        - github.com/Ruk1ng001_64306261336663
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3665393533353837"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3761356632326233"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3233396336383361"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3763343961633537"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3139323165616438"
        - github.com/Ruk1ng001_3835353935393363
        - github.com/Ruk1ng001_3763646137393936
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6566633964303465
        - github.com/Ruk1ng001_3530303439646561
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - github.com/Ruk1ng001_6138303631343566
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6230396164353864"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_39386462363063"
        - github.com/Ruk1ng001_3862646366326534
        - github.com/Ruk1ng001_6639353032613162
        - github.com/Ruk1ng001_6136633862646666
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - github.com/Ruk1ng001_6636653333643238
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3764386435366434"
        - github.com/Ruk1ng001_3562653962643961
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3935313436616265"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_33626633386536"
        - github.com/Ruk1ng001_3139343834313837
        - github.com/Ruk1ng001_3562626665316265
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6163336134623236"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3131613032366237"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_6538346433393031
        - "❓_\U0001F1F0\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3634383734346538"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_3736626233366431
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635363061343766"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_3565393236356239
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_31363266336135
        - "\U0001F7E0_\U0001F1EB\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3463343735646631"
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_3634303766363039
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_6538633765366135
        - github.com/Ruk1ng001_34303031306332
        - github.com/Ruk1ng001_3734343932623736
        - github.com/Ruk1ng001_3733333936373361
        - "❓_\U0001F1EA\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3563656464656539"
        - github.com/Ruk1ng001_3665643435623533
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3861656566336337"
        - github.com/Ruk1ng001_3639373338336331
        - github.com/Ruk1ng001_3162626630326566
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6636306231303866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_3431636465393261
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_3736303961346262
        - "❓_\U0001F1E7\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6665643062356164"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3938613930633965"
        - github.com/Ruk1ng001_6335636439313531
        - github.com/Ruk1ng001_6335313363343366
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - github.com/Ruk1ng001_6630326431336330
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6261346464636137"
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3137653963353266
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_6437306131613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_3137383833356432"
        - "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6635626362326138"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3232626433343733"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233393865653937"
        - "❓\U0001F4BB_github.com/Ruk1ng001_6438323161313661"
        - github.com/Ruk1ng001_3338626463343632
        - github.com/Ruk1ng001_3331373033323837
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937633561373936"
        - github.com/Ruk1ng001_3230393564333632
        - github.com/Ruk1ng001_6535643933383238
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - github.com/Ruk1ng001_31353633376634
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - github.com/Ruk1ng001_3465343033313861
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6533343938633961"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3534336530613239"
        - "❓_\U0001F1FA\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_6635633432326663"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3635363063323866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3864393162363063"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3238666265643830"
        - github.com/Ruk1ng001_6166666635613931
        - "\U0001F7E1_\U0001F1EE\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3339303930333736"
        - github.com/Ruk1ng001_3533643165623038
        - github.com/Ruk1ng001_3435326431633532
        - github.com/Ruk1ng001_3832326663663061
        - github.com/Ruk1ng001_3634663933633935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3330343539373730
        - github.com/Ruk1ng001_6666306537316635
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_6162666636316566"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234396461633933"
        - github.com/Ruk1ng001_6462316532396330
        - "\U0001F7E0_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_3364383665656639"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3531366264386430"
        - github.com/Ruk1ng001_3733353962346137
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3533636666656439"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - github.com/Ruk1ng001_3432613333366433
        - github.com/Ruk1ng001_3533383239303138
        - github.com/Ruk1ng001_3237343865636465
        - github.com/Ruk1ng001_6239313963373066
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_39383432633836"
        - github.com/Ruk1ng001_6332626561616562
        - github.com/Ruk1ng001_3238646331663237
      test-timeout: 5
      tolerance: 50
      type: url-test
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F3AC"
      proxies:
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_3565343138323138"
        - github.com/Ruk1ng001_3463336131343434
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3333356432343431"
        - github.com/Ruk1ng001_6434363765663130
        - github.com/Ruk1ng001_3533323030353139
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_3466326438663839
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_6337616130386238"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_63633939303633"
        - github.com/Ruk1ng001_6235616432666637
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - github.com/Ruk1ng001_6536333635643530
        - "\U0001F7E1\U0001F4BB_github.com/Ruk1ng001_6531623064303132"
        - github.com/Ruk1ng001_3438306631343031
        - github.com/Ruk1ng001_6563616339333962
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3461393963333934"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_3937353231343736
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6565636133346561"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234346334313165"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3737623131356232"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3265373135626263"
        - github.com/Ruk1ng001_32376632636230
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_6163333066653934
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_35************"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3365343263363465"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3339653965346362"
        - github.com/Ruk1ng001_3362353661633536
        - github.com/Ruk1ng001_3431316235386230
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3138316430316563"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3663316466623139"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3137303533393364"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_31633031356434
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_6437303763303333
        - github.com/Ruk1ng001_6465373134613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6231393332623436
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534303235613966"
        - github.com/Ruk1ng001_6439636165306266
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3332326331663966"
        - github.com/Ruk1ng001_3236636263303765
        - github.com/Ruk1ng001_32663463313464
        - github.com/Ruk1ng001_3234623938643265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6531323934636335"
        - github.com/Ruk1ng001_3464386330303961
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3334306131373239"
        - github.com/Ruk1ng001_6539636434623761
        - github.com/Ruk1ng001_3863393430613433
        - github.com/Ruk1ng001_6139646430646237
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_36336439643765"
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3332626436666537"
        - github.com/Ruk1ng001_3530386133386132
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3438363833356166"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - github.com/Ruk1ng001_65353737353831
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3438633666303736"
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6364616230623465"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_64356134396635"
        - github.com/Ruk1ng001_6430353337376138
        - github.com/Ruk1ng001_6662376237646330
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3438313436343536"
        - github.com/Ruk1ng001_3738303331643362
        - github.com/Ruk1ng001_3937396532366231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3933383531336336
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4B0_github.com/Ruk1ng001_3333396564343966"
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - github.com/Ruk1ng001_3136363866356365
        - github.com/Ruk1ng001_6136636466633939
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3532323836336562
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_6662316438616339"
        - github.com/Ruk1ng001_3639386130316131
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_6132663631643736
        - github.com/Ruk1ng001_6464323733386635
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3536663336653435
        - github.com/Ruk1ng001_3163666237653361
        - github.com/Ruk1ng001_64306261336663
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3665393533353837"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3761356632326233"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3233396336383361"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3763343961633537"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3139323165616438"
        - github.com/Ruk1ng001_3835353935393363
        - github.com/Ruk1ng001_3763646137393936
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6566633964303465
        - github.com/Ruk1ng001_3530303439646561
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - github.com/Ruk1ng001_6138303631343566
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6230396164353864"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_39386462363063"
        - github.com/Ruk1ng001_3862646366326534
        - github.com/Ruk1ng001_6639353032613162
        - github.com/Ruk1ng001_6136633862646666
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3764386435366434"
        - github.com/Ruk1ng001_3562653962643961
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3935313436616265"
        - github.com/Ruk1ng001_3139343834313837
        - github.com/Ruk1ng001_3562626665316265
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6163336134623236"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3131613032366237"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_6538346433393031
        - github.com/Ruk1ng001_3139396234313438
        - "❓_\U0001F1F0\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3634383734346538"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_3736626233366431
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635363061343766"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_3565393236356239
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_31363266336135
        - "\U0001F7E0_\U0001F1EB\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3463343735646631"
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_3634303766363039
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_6538633765366135
        - github.com/Ruk1ng001_34303031306332
        - github.com/Ruk1ng001_3734343932623736
        - github.com/Ruk1ng001_3733333936373361
        - github.com/Ruk1ng001_3636623563313461
        - "❓_\U0001F1EA\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3563656464656539"
        - github.com/Ruk1ng001_3665643435623533
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3861656566336337"
        - github.com/Ruk1ng001_3639373338336331
        - github.com/Ruk1ng001_3162626630326566
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6636306231303866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_3431636465393261
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_3736303961346262
        - "❓_\U0001F1E7\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6665643062356164"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3938613930633965"
        - github.com/Ruk1ng001_6335636439313531
        - github.com/Ruk1ng001_6335313363343366
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6261346464636137"
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3137653963353266
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - github.com/Ruk1ng001_3333326330366538
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6635626362326138"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3232626433343733"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233393865653937"
        - "❓\U0001F4BB_github.com/Ruk1ng001_6438323161313661"
        - github.com/Ruk1ng001_3338626463343632
        - github.com/Ruk1ng001_3331373033323837
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937633561373936"
        - github.com/Ruk1ng001_3230393564333632
        - github.com/Ruk1ng001_6535643933383238
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - github.com/Ruk1ng001_31353633376634
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - github.com/Ruk1ng001_3465343033313861
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6533343938633961"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3534336530613239"
        - "❓_\U0001F1FA\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_6635633432326663"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3635363063323866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3864393162363063"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3238666265643830"
        - "\U0001F7E1_\U0001F1EE\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3339303930333736"
        - github.com/Ruk1ng001_3533643165623038
        - github.com/Ruk1ng001_3435326431633532
        - github.com/Ruk1ng001_3832326663663061
        - github.com/Ruk1ng001_3634663933633935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3330343539373730
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_6162666636316566"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234396461633933"
        - github.com/Ruk1ng001_6462316532396330
        - "\U0001F7E0_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_3364383665656639"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3531366264386430"
        - github.com/Ruk1ng001_3733353962346137
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3533636666656439"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - github.com/Ruk1ng001_3432613333366433
        - github.com/Ruk1ng001_3533383239303138
        - github.com/Ruk1ng001_3237343865636465
        - github.com/Ruk1ng001_6239313963373066
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_39383432633836"
        - github.com/Ruk1ng001_6332626561616562
        - github.com/Ruk1ng001_3238646331663237
      test-timeout: 5
      tolerance: 50
      type: url-test
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F419"
      proxies:
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_3565343138323138"
        - github.com/Ruk1ng001_3463336131343434
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3333356432343431"
        - github.com/Ruk1ng001_6339646565373938
        - github.com/Ruk1ng001_3533323030353139
        - github.com/Ruk1ng001_3466326438663839
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_6337616130386238"
        - github.com/Ruk1ng001_6634356239386330
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_63633939303633"
        - github.com/Ruk1ng001_6235616432666637
        - github.com/Ruk1ng001_6536333635643530
        - "\U0001F7E1\U0001F4BB_github.com/Ruk1ng001_6531623064303132"
        - github.com/Ruk1ng001_6636333131363533
        - github.com/Ruk1ng001_3438306631343031
        - github.com/Ruk1ng001_6563616339333962
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_3937353231343736
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6565636133346561"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234346334313165"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3737623131356232"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3265373135626263"
        - github.com/Ruk1ng001_32376632636230
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_6163333066653934
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_35************"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3365343263363465"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3339653965346362"
        - github.com/Ruk1ng001_3431316235386230
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3138316430316563"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3663316466623139"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3137303533393364"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_31633031356434
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_6437303763303333
        - github.com/Ruk1ng001_6465373134613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6231393332623436
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6466343131636431"
        - github.com/Ruk1ng001_6366646333613962
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_6439636165306266
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3332326331663966"
        - github.com/Ruk1ng001_3236636263303765
        - github.com/Ruk1ng001_32663463313464
        - github.com/Ruk1ng001_3234623938643265
        - github.com/Ruk1ng001_3464386330303961
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3334306131373239"
        - github.com/Ruk1ng001_6539636434623761
        - github.com/Ruk1ng001_3863393430613433
        - github.com/Ruk1ng001_6139646430646237
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_36336439643765"
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3332626436666537"
        - github.com/Ruk1ng001_3530386133386132
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3438363833356166"
        - github.com/Ruk1ng001_65353737353831
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3438633666303736"
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6364616230623465"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_64356134396635"
        - github.com/Ruk1ng001_6430353337376138
        - github.com/Ruk1ng001_6662376237646330
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3438313436343536"
        - github.com/Ruk1ng001_3362383631666366
        - github.com/Ruk1ng001_3738303331643362
        - github.com/Ruk1ng001_3937396532366231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3933383531336336
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_3337366466613035"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4B0_github.com/Ruk1ng001_3333396564343966"
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - github.com/Ruk1ng001_3136363866356365
        - github.com/Ruk1ng001_6136636466633939
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3532323836336562
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_6662316438616339"
        - github.com/Ruk1ng001_3639386130316131
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_3938613662353534
        - github.com/Ruk1ng001_6132663631643736
        - github.com/Ruk1ng001_6464323733386635
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3536663336653435
        - github.com/Ruk1ng001_3163666237653361
        - github.com/Ruk1ng001_64306261336663
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3665393533353837"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3233396336383361"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3763343961633537"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3139323165616438"
        - github.com/Ruk1ng001_3835353935393363
        - github.com/Ruk1ng001_3763646137393936
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6566633964303465
        - github.com/Ruk1ng001_3530303439646561
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6230396164353864"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_39386462363063"
        - github.com/Ruk1ng001_3862646366326534
        - github.com/Ruk1ng001_6639353032613162
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - github.com/Ruk1ng001_6636653333643238
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3764386435366434"
        - github.com/Ruk1ng001_3562653962643961
        - github.com/Ruk1ng001_3139343834313837
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6163336134623236"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3131613032366237"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_6538346433393031
        - "❓_\U0001F1F0\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3634383734346538"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_3736626233366431
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_3565393236356239
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_31363266336135
        - "\U0001F7E0_\U0001F1EB\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3463343735646631"
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_3634303766363039
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_6538633765366135
        - github.com/Ruk1ng001_34303031306332
        - github.com/Ruk1ng001_3734343932623736
        - github.com/Ruk1ng001_3733333936373361
        - github.com/Ruk1ng001_3665643435623533
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3861656566336337"
        - github.com/Ruk1ng001_3639373338336331
        - github.com/Ruk1ng001_3162626630326566
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6636306231303866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_3431636465393261
        - github.com/Ruk1ng001_3736303961346262
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3938613930633965"
        - github.com/Ruk1ng001_6335636439313531
        - github.com/Ruk1ng001_6335313363343366
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - github.com/Ruk1ng001_6630326431336330
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6261346464636137"
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3137653963353266
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_6437306131613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_3137383833356432"
        - "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6635626362326138"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3232626433343733"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233393865653937"
        - "❓\U0001F4BB_github.com/Ruk1ng001_6438323161313661"
        - github.com/Ruk1ng001_3338626463343632
        - github.com/Ruk1ng001_3331373033323837
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937633561373936"
        - github.com/Ruk1ng001_3230393564333632
        - github.com/Ruk1ng001_6535643933383238
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - github.com/Ruk1ng001_3465343033313861
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6533343938633961"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3534336530613239"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3635363063323866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3864393162363063"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - github.com/Ruk1ng001_3533643165623038
        - github.com/Ruk1ng001_3435326431633532
        - github.com/Ruk1ng001_3832326663663061
        - github.com/Ruk1ng001_3634663933633935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3330343539373730
        - github.com/Ruk1ng001_6666306537316635
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_6162666636316566"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234396461633933"
        - github.com/Ruk1ng001_6462316532396330
        - "\U0001F7E0_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_3364383665656639"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3531366264386430"
        - github.com/Ruk1ng001_3733353962346137
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - github.com/Ruk1ng001_3432613333366433
        - github.com/Ruk1ng001_3533383239303138
        - github.com/Ruk1ng001_3237343865636465
        - github.com/Ruk1ng001_6239313963373066
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_39383432633836"
        - github.com/Ruk1ng001_6332626561616562
        - github.com/Ruk1ng001_3238646331663237
      test-timeout: 5
      tolerance: 50
      type: url-test
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: ✈️
      proxies:
        - "❓_\U0001F1E6\U0001F1F9_\U0001F4BC_github.com/Ruk1ng001_3565343138323138"
        - github.com/Ruk1ng001_3463336131343434
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3333356432343431"
        - github.com/Ruk1ng001_6339646565373938
        - github.com/Ruk1ng001_3533323030353139
        - github.com/Ruk1ng001_3466326438663839
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_6337616130386238"
        - github.com/Ruk1ng001_6634356239386330
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_63633939303633"
        - github.com/Ruk1ng001_6235616432666637
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - github.com/Ruk1ng001_6536333635643530
        - "\U0001F7E1\U0001F4BB_github.com/Ruk1ng001_6531623064303132"
        - github.com/Ruk1ng001_6636333131363533
        - github.com/Ruk1ng001_6563616339333962
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - "\U0001F534_\U0001F1F8\U0001F1EC_\U0001F4BB_github.com/Ruk1ng001_3461393963333934"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_3937353231343736
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6565636133346561"
        - "\U0001F7E1_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6261373366613764"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234346334313165"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3737623131356232"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3265373135626263"
        - github.com/Ruk1ng001_32376632636230
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_6163333066653934
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_35************"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3365343263363465"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3339653965346362"
        - github.com/Ruk1ng001_3362353661633536
        - github.com/Ruk1ng001_3431316235386230
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3138316430316563"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3663316466623139"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3137303533393364"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_6437303763303333
        - github.com/Ruk1ng001_6465373134613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6231393332623436
        - github.com/Ruk1ng001_6366646333613962
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534303235613966"
        - github.com/Ruk1ng001_6439636165306266
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3332326331663966"
        - github.com/Ruk1ng001_3236636263303765
        - github.com/Ruk1ng001_32663463313464
        - github.com/Ruk1ng001_3234623938643265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6531323934636335"
        - github.com/Ruk1ng001_3464386330303961
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3334306131373239"
        - github.com/Ruk1ng001_6539636434623761
        - github.com/Ruk1ng001_3863393430613433
        - github.com/Ruk1ng001_6139646430646237
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_36336439643765"
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3332626436666537"
        - github.com/Ruk1ng001_3530386133386132
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3438363833356166"
        - github.com/Ruk1ng001_65353737353831
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3438633666303736"
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6364616230623465"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_64356134396635"
        - github.com/Ruk1ng001_6430353337376138
        - github.com/Ruk1ng001_6662376237646330
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3438313436343536"
        - github.com/Ruk1ng001_3362383631666366
        - github.com/Ruk1ng001_3738303331643362
        - github.com/Ruk1ng001_3937396532366231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3933383531336336
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4B0_github.com/Ruk1ng001_3333396564343966"
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - github.com/Ruk1ng001_3136363866356365
        - github.com/Ruk1ng001_6136636466633939
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3532323836336562
        - github.com/Ruk1ng001_3639386130316131
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_6132663631643736
        - github.com/Ruk1ng001_6464323733386635
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3536663336653435
        - github.com/Ruk1ng001_3163666237653361
        - github.com/Ruk1ng001_64306261336663
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3665393533353837"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3761356632326233"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3233396336383361"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3763343961633537"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3139323165616438"
        - github.com/Ruk1ng001_3835353935393363
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6566633964303465
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - github.com/Ruk1ng001_6138303631343566
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6230396164353864"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - "\U0001F534_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_39386462363063"
        - github.com/Ruk1ng001_3862646366326534
        - github.com/Ruk1ng001_6639353032613162
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - github.com/Ruk1ng001_6636653333643238
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3764386435366434"
        - github.com/Ruk1ng001_3562653962643961
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3935313436616265"
        - github.com/Ruk1ng001_3139343834313837
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6163336134623236"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3131613032366237"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_6538346433393031
        - "❓_\U0001F1F0\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3634383734346538"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_3736626233366431
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635363061343766"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_3565393236356239
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_31363266336135
        - "\U0001F7E0_\U0001F1EB\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3463343735646631"
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_6538633765366135
        - github.com/Ruk1ng001_34303031306332
        - github.com/Ruk1ng001_3734343932623736
        - github.com/Ruk1ng001_3733333936373361
        - "❓_\U0001F1EA\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3563656464656539"
        - github.com/Ruk1ng001_3665643435623533
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3861656566336337"
        - github.com/Ruk1ng001_3639373338336331
        - github.com/Ruk1ng001_3162626630326566
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6636306231303866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_3431636465393261
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_3736303961346262
        - "❓_\U0001F1E7\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6665643062356164"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3938613930633965"
        - github.com/Ruk1ng001_6335636439313531
        - github.com/Ruk1ng001_6335313363343366
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - github.com/Ruk1ng001_6630326431336330
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6261346464636137"
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3137653963353266
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_6437306131613231
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6635626362326138"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3232626433343733"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233393865653937"
        - "❓\U0001F4BB_github.com/Ruk1ng001_6438323161313661"
        - github.com/Ruk1ng001_3338626463343632
        - github.com/Ruk1ng001_3331373033323837
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937633561373936"
        - github.com/Ruk1ng001_3230393564333632
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - github.com/Ruk1ng001_31353633376634
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - github.com/Ruk1ng001_3465343033313861
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6533343938633961"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3534336530613239"
        - "❓_\U0001F1FA\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_6635633432326663"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3635363063323866"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC_\U0001F4F6_github.com/Ruk1ng001_3864393162363063"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3238666265643830"
        - "\U0001F7E1_\U0001F1EE\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3339303930333736"
        - github.com/Ruk1ng001_3533643165623038
        - github.com/Ruk1ng001_3435326431633532
        - github.com/Ruk1ng001_3634663933633935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3330343539373730
        - github.com/Ruk1ng001_6666306537316635
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_6162666636316566"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3234396461633933"
        - github.com/Ruk1ng001_6462316532396330
        - "\U0001F7E0_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_3364383665656639"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3531366264386430"
        - github.com/Ruk1ng001_3733353962346137
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3533636666656439"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - github.com/Ruk1ng001_3432613333366433
        - github.com/Ruk1ng001_3533383239303138
        - github.com/Ruk1ng001_3237343865636465
        - github.com/Ruk1ng001_6239313963373066
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_39383432633836"
        - github.com/Ruk1ng001_6332626561616562
        - github.com/Ruk1ng001_3238646331663237
      test-timeout: 5
      tolerance: 50
      type: url-test
      url: https://www.gstatic.com/generate_204
rules:
    - DOMAIN-SUFFIX,csdn.net,DIRECT
    - DOMAIN-SUFFIX,juejin.cn,DIRECT
    - DOMAIN-SUFFIX,linux.do,DIRECT
    - DOMAIN-SUFFIX,plugins.jetbrains.com,DIRECT
    - DOMAIN-SUFFIX,shared.oaifree.com,DIRECT
    - RULE-SET,apple_cdn,DIRECT
    - RULE-SET,apple_services,DIRECT
    - RULE-SET,domestic_ip,DIRECT
    - RULE-SET,lan_ip,DIRECT
    - RULE-SET,apple_cn_non_ip,DIRECT,DIRECT
    - RULE-SET,direct_non_ip,DIRECT
    - RULE-SET,domestic_non_ip,DIRECT
    - RULE-SET,lan_non_ip,DIRECT
    - RULE-SET,microsoft_cdn_non_ip,DIRECT
    - RULE-SET,microsoft_non_ip,DIRECT
    - RULE-SET,reject_domainset,REJECT
    - RULE-SET,reject_ip,REJECT
    - RULE-SET,sogouinput,REJECT
    - RULE-SET,reject_non_ip,REJECT
    - RULE-SET,reject_non_ip_no_drop,REJECT
    - "DOMAIN-SUFFIX,api.themoviedb.org,\U0001F3AC"
    - "DOMAIN-SUFFIX,github.com,\U0001F419"
    - "DOMAIN-SUFFIX,raw.githubusercontent.com,\U0001F419"
    - "RULE-SET,cdn_domainset,\U0001F3AF"
    - "RULE-SET,download_domainset,\U0001F3AF"
    - "RULE-SET,stream_ip,\U0001F4FA"
    - RULE-SET,telegram_ip,✈️
    - "RULE-SET,ai_non_ip,\U0001F916"
    - "RULE-SET,download_non_ip,\U0001F3AF"
    - "RULE-SET,global_non_ip,\U0001F3AF"
    - RULE-SET,reject_non_ip_drop,REJECT-DROP
    - "RULE-SET,stream_non_ip,\U0001F4FA"
    - RULE-SET,telegram_non_ip,✈️
    - "MATCH,\U0001F3AF"
dns:
    enable: true
    prefer-h3: true
    ipv6-timeout: 100
    use-hosts: true
    use-system-hosts: true
    respect-rules: true
    nameserver:
        - system
        - https://*********/dns-query
        - https://doh.pub/dns-query
    fallback-filter:
        geoip: true
        geoip-code: CN
    enhanced-mode: fake-ip
    fake-ip-range: ********/8
    fake-ip-filter:
        - dns.msftnsci.com
        - www.msftnsci.com
        - www.msftconnecttest.com
    default-nameserver:
        - ***************
        - *******
    proxy-server-nameserver:
        - https://*********/dns-query
        - https://doh.pub/dns-query
ntp:
    server: time.apple.com
    port: 123
    interval: 30
tun:
    dns-hijack:
        - 0.0.0.0:53
    auto-route: true
    auto-detect-interface: true
    inet6-address:
        - fdfe:dcba:9876::1/126
tuic-server:
    max-idle-time: 15000
    authentication-timeout: 1000
    alpn:
        - h3
    max-udp-relay-packet-size: 1500
iptables:
    inbound-interface: lo
    dns-redirect: true
experimental:
    quic-go-disable-ecn: true
profile:
    store-selected: true
    store-fake-ip: true
geox-url:
    geoip: https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip.dat
    mmdb: https://cdn.jsdelivr.net/gh/Loyalsoldier/geoip@release/Country.mmdb
    asn: https://cdn.jsdelivr.net/gh/Loyalsoldier/geoip@release/GeoLite2-ASN.mmdb
    geosite: https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geosite.dat
sniffer:
    enable: true
    override-destination: true
    skip-domain:
        - Mijia Cloud
        - +.push.apple.com
    force-dns-mapping: true
    parse-pure-ip: true
    sniff:
        HTTP:
            ports:
                - "80"
                - 8080-8880
            override-destination: true
        QUIC:
            ports:
                - "443"
                - "8443"
        TLS:
            ports:
                - "443"
                - "8443"
