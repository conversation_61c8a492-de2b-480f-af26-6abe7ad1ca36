Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-08-01T22:33:06.718554200+08:00" level=info msg="Start initial configuration in progress"
time="2025-08-01T22:33:06.724700500+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-08-01T22:33:06.724700500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-08-01T22:33:06.725211700+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-08-01T22:33:06.729405700+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7480"
time="2025-08-01T22:33:06.729405700+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-08-01T22:33:07.018215300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118417"
time="2025-08-01T22:33:07.022767100+08:00" level=info msg="Initial configuration complete, total time: 301ms"
time="2025-08-01T22:33:07.024769700+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-08-01T22:33:07.024769700+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-08-01T22:33:07.024769700+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-08-01T22:33:07.047632100+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-08-01T22:33:07.685478700+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-01T22:33:07.816187800+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-08-01T22:33:07.818261900+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-08-01T22:33:07.821332900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-01T22:33:07.821332900+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-08-01T22:33:07.830146200+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-08-01T22:33:07.832632800+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-08-01T22:33:07.832632800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-01T22:33:07.841809000+08:00" level=warning msg="[Provider] free-6 not updated for a long time, force refresh"
time="2025-08-01T22:33:07.845460500+08:00" level=warning msg="[Provider] cordcloud not updated for a long time, force refresh"
time="2025-08-01T22:33:07.882500500+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-08-01T22:33:08.748604300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:08.749106300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:08.749106300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-01T22:33:08.749106300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:08.749106300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:08.749106300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:09.077678400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-01T22:33:09.089704800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:33:09.089704800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:33:09.089704800+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-01T22:33:09.090573500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:33:09.090573500+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-01T22:33:09.090573500+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-01T22:33:09.090573500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:33:09.090573500+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-01T22:33:09.433993200+08:00" level=warning msg="because CC CordCloud failed multiple times, active health check"
time="2025-08-01T22:33:09.763691600+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-01T22:33:09.865879600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:09.865879600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-01T22:33:09.865879600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:09.871742200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-01T22:33:09.921829400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:09.921829400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:10.842375500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:11.179639000+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-01T22:33:12.307673500+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-08-01T22:33:14.103764200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-01T22:33:14.103764200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:14.103764200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:14.103764200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:14.103764200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:14.103764200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-01T22:33:14.103764200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:14.103764200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:14.105408500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.105921600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.105921600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-08-01T22:33:14.105921600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-08-01T22:33:14.105921600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.107964800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-08-01T22:33:14.107964800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.107964800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.107964800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.108512900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-08-01T22:33:14.152458500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:14.152458500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:14.152458500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:14.152458500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:14.152458500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:14.152458500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:14.250331800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.250331800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-08-01T22:33:14.250331800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.250331800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.250331800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-08-01T22:33:14.251364700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.251878100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.252392400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-08-01T22:33:14.252907600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.252907600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-08-01T22:33:14.334933500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-08-01T22:33:14.334933500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.334933500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.334933500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-08-01T22:33:14.334933500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.336970600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.336970600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-08-01T22:33:14.336970600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.337485700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-08-01T22:33:14.337485700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-08-01T22:33:14.720526800+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-08-01T22:33:15.099534300+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:15.209412300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-08-01T22:33:15.236907100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:15.237923000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:15.238952300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-01T22:33:15.238952300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:15.238952300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:15.239467100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:15.435308900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-08-01T22:33:15.747633600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: operation was canceled"
time="2025-08-01T22:33:15.870317200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:15.872444400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-08-01T22:33:15.874996100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-08-01T22:33:17.774793000+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_35323363303265 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:17.774793000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:17.817002500+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": net/http: TLS handshake timeout"
time="2025-08-01T22:33:17.842069000+08:00" level=error msg="[Provider] free-6 pull error: Get \"https://gh-proxy.com/raw.githubusercontent.com/Ruk1ng001/freeSub/main/clash.yaml\": net/http: TLS handshake timeout"
time="2025-08-01T22:33:19.690601900+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-01T22:33:20.859229100+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-08-01T22:33:20.859229100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.859229100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.859229100+08:00" level=warning msg="because 🇭🇰 香港 failed multiple times, active health check"
time="2025-08-01T22:33:20.859229100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.859754200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.863383800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.863383800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.863897300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.864938800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.865974600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.865974600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.866492800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.867004900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.867519000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.868034500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.868548300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.868548300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.869573800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.870100200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.870620700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.870620700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.871653900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.871653900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.871653900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.872182600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.873206700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:20.873206700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:20.873206700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.874750900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.875260700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:20.920343800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:20.920343800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.921918600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.924000900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:20.941889400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-08-01T22:33:20.967525300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:20.969619400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.969619400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.969619400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.971161300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.971671600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.971671600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.972693200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.972693200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.973200700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:20.974228800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.974738100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:20.974738100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.975762100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.978328500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:20.980368500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:20.982417800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:20.983950400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:20.987091300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.018048500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.019077000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.020111100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.020623600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.021645200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.022154900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.024203500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.026240400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.027770300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.031404800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.063113400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.064129400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.065659300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.067190600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.068218800+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-08-01T22:33:21.068734400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.069244700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.070778300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.070778300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.072309000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.072819800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.078302800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.078302800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.079327500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.492730600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-08-01T22:33:21.494824700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.496872600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.498921100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.500976100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.504046500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.505582800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.506605600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.554739200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.556299000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.558582800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.609181500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.611240700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.613801000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.659697700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: operation was canceled"
time="2025-08-01T22:33:21.662256100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.663788600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:21.664818200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp **************:52011: i/o timeout"
time="2025-08-01T22:33:22.425665600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3439626230313037 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:22.425665600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:25.964127500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6635363061343766 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:25.964127500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:26.275967400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-08-01T22:33:26.275967400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-08-01T22:33:26.275967400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-08-01T22:33:26.275967400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-08-01T22:33:26.275967400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-08-01T22:33:26.275967400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-08-01T22:33:26.772718600+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": net/http: TLS handshake timeout"
time="2025-08-01T22:33:27.620022100+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-08-01T22:33:27.621563000+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-08-01T22:33:27.622075200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-01T22:33:27.626288700+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-08-01T22:33:27.629346700+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-08-01T22:33:27.629859400+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-08-01T22:33:27.629859400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-01T22:33:27.636484900+08:00" level=warning msg="[Provider] cordcloud not updated for a long time, force refresh"
time="2025-08-01T22:33:27.667350100+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-08-01T22:33:27.845930000+08:00" level=error msg="[Provider] cordcloud pull error: context deadline exceeded"
time="2025-08-01T22:33:27.867136800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:33:27.867136800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:33:27.867136800+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-01T22:33:27.867136800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-01T22:33:27.867136800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:33:27.867136800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-01T22:33:27.867136800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:33:27.867136800+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-01T22:33:28.966742300+08:00" level=error msg="CC | 广州移动转日本TE[M][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:28.966742300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:29.143073100+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-01T22:33:29.145130700+08:00" level=error msg="MLY | 套餐到期：2025-08-02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": io: read/write on closed pipe"
time="2025-08-01T22:33:29.145130700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:29.145130700+08:00" level=error msg="MLY | 官网:https://ml13.hfhfb.homes failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": io: read/write on closed pipe"
time="2025-08-01T22:33:29.145130700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:29.145130700+08:00" level=error msg="MLY | 剩余流量：1998.79 GB failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": io: read/write on closed pipe"
time="2025-08-01T22:33:29.145638700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:29.146146700+08:00" level=error msg="MLY | 无网更新订阅 | 禁止回国 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": io: read/write on closed pipe"
time="2025-08-01T22:33:29.146146700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:29.252239800+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-01T22:33:29.463074100+08:00" level=warning msg="because CC CordCloud failed multiple times, active health check"
time="2025-08-01T22:33:29.814565200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:29.831376200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:29.831376200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-01T22:33:29.831376200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:29.831376200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:29.831376200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:29.831376200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:30.287969700+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-01T22:33:30.950851400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:33:30.950851400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:33:32.271830000+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-08-01T22:33:33.576199100+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:33.576199100+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:33.576199100+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:33.576199100+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:33.739341000+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:34.143423300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-08-01T22:33:34.143423300+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-01T22:33:34.145981600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:34.146491900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:33:35.142752700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:33:35.142752700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:33:35.142752700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:33:35.142752700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:33:35.142752700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:33:35.142752700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:33:35.142752700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:33:35.142752700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:33:35.641445300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3533323030353139 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:35.641445300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:36.878788100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:33:36.894671300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-08-01T22:33:39.205800100+08:00" level=info msg="Start initial configuration in progress"
time="2025-08-01T22:33:39.211967300+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-08-01T22:33:39.211967300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-08-01T22:33:39.211967300+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-08-01T22:33:39.216062400+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7482"
time="2025-08-01T22:33:39.216583100+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-08-01T22:33:39.416216100+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118417"
time="2025-08-01T22:33:39.419308900+08:00" level=info msg="Initial configuration complete, total time: 210ms"
time="2025-08-01T22:33:39.435722200+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-08-01T22:33:39.967849200+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-08-01T22:33:39.969966900+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-08-01T22:33:39.971669900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-01T22:33:39.974409700+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-08-01T22:33:39.974409700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-01T22:33:39.974409700+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-08-01T22:33:39.983387300+08:00" level=warning msg="[Provider] cordcloud not updated for a long time, force refresh"
time="2025-08-01T22:33:39.983927200+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-08-01T22:33:40.020501100+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-08-01T22:33:40.039123100+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-01T22:33:41.387739700+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-01T22:33:41.414105200+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-01T22:33:41.414613700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:33:41.414613700+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-01T22:33:41.414613700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:33:41.414613700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-01T22:33:41.414613700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:33:41.414613700+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-01T22:33:41.414613700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:33:41.415124600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-01T22:33:41.882971200+08:00" level=error msg="initial proxy provider free-5 error: 404 Not Found"
time="2025-08-01T22:33:42.222961400+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-01T22:33:42.796085600+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-01T22:33:42.834292600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: context canceled"
time="2025-08-01T22:33:42.834292600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> cloudflare-dns.com:443 error: context canceled"
time="2025-08-01T22:33:42.834292600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: context canceled"
time="2025-08-01T22:33:42.834292600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: context canceled"
time="2025-08-01T22:33:42.834292600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: context canceled"
time="2025-08-01T22:33:43.164185700+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-01T22:33:44.969294900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-08-01T22:33:44.969294900+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966424600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="🎯 Webshare-US专线-Direct failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966424600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="🔗 Landing-HK-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966424600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966424600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="🏠 Landing-Home-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966424600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="🇺🇸 Landing-US-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966424600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966424600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966424600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966424600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966424600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="🚄 Landing-Fast-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966946700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966946700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966946700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966946700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966946700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966946700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="🚀 Landing-Auto-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966946700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966946700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966424600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966946700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966946700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966946700+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966946700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966946700+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966946700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966946700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966946700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966946700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966946700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966946700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.966946700+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.966946700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.967460900+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.967460900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.967460900+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.967460900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.967971600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.967971600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.967971600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.967971600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.967971600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.967971600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.967971600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.967971600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.967971600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.967971600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.967971600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.967971600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:47.967971600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:47.967971600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:48.352933800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:48.352933800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-01T22:33:48.352933800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:48.352933800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:48.352933800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:48.352933800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-01T22:33:48.352933800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:48.352933800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:48.352933800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:48.352933800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:48.352933800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:48.352933800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:49.589696200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:58458(clash-verge.exe) --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-08-01T22:33:49.966470700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:49.966470700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:49.966470700+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:49.966470700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:49.966984700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:49.966984700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:49.966984700+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:49.966984700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:49.968009600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:49.968009600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:49.968009600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:49.968009600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:49.969532900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-08-01T22:33:49.969532900+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-01T22:33:51.712459700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:51.712459700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:51.712459700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:51.712459700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:51.712459700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:51.712459700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-01T22:33:51.712459700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:51.712459700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:51.712459700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:51.712459700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-01T22:33:51.712459700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-01T22:33:51.712459700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-01T22:33:54.349511800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) 127.0.0.1:58931(clash-verge.exe) --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-08-01T22:33:55.629178400+08:00" level=error msg="MLY | SG - 新加坡 01 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:55.629178400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:56.210251200+08:00" level=error msg="MLY | SG - 新加坡 02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:56.210251200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:56.550264700+08:00" level=error msg="MLY | SG - 新加坡 03 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:33:56.550264700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:33:59.983926900+08:00" level=error msg="[Provider] cordcloud pull error: context deadline exceeded"
time="2025-08-01T22:34:00.020718900+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-08-01T22:34:03.170156600+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-08-01T22:34:07.900474700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6431323961656438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:34:07.900474700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:34:09.385440900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:34:09.385440900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:34:09.385440900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:34:09.385440900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:34:09.385440900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:34:09.385440900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-01T22:34:19.984526000+08:00" level=error msg="[Provider] cordcloud pull error: context deadline exceeded"
time="2025-08-01T22:34:21.415072100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:34:21.415072100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-01T22:34:21.415611800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:34:21.415611800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-01T22:34:22.541994500+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-01T22:34:23.598958300+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-01T22:34:25.046210700+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-01T22:34:28.716586200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3763646137393936 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:34:28.716586200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:34:34.970181000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-08-01T22:34:34.970181000+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-01T22:34:38.843000500+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6635363061343766 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:34:38.843000500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:34:45.883506200+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-08-01T22:34:48.318770100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3938356531643832 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:34:48.318770100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:34:48.843231700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3631396266633036 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:34:48.843231700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:34:58.318994700+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6533343938633961 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:34:58.318994700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:35:05.764431000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6264653634666432 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:35:05.764431000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:35:41.415370000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:35:41.415370000+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-01T22:35:41.415898000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:35:41.415898000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-01T22:35:42.861289700+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-01T22:35:44.396729300+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-01T22:35:47.268039600+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-01T22:35:48.907398100+08:00" level=warning msg="[TCP] dial 💻 Copilot (match DomainKeyword/copilot) **********:61951(Code.exe) --> telemetry.individual.githubcopilot.com:443 error: read tcp *************:61952->**************:56011: wsarecv: An existing connection was forcibly closed by the remote host."
time="2025-08-01T22:35:59.970824600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-08-01T22:35:59.970824600+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-01T22:36:25.884603300+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-08-01T22:36:43.179022500+08:00" level=warning msg="[TCP] dial 💻 Copilot (match RuleSet/copilot) **********:52351(Cursor.exe) --> api.segment.io:443 error: read tcp *************:52352->**************:56011: wsarecv: An existing connection was forcibly closed by the remote host."
time="2025-08-01T22:36:45.094678700+08:00" level=warning msg="[TCP] dial 💻 Copilot (match RuleSet/copilot) **********:52351(Cursor.exe) --> api.segment.io:443 error: context deadline exceeded"
time="2025-08-01T22:36:48.278087400+08:00" level=warning msg="[TCP] dial 💻 Copilot (match RuleSet/copilot) **********:52419(Cursor.exe) --> api.segment.io:443 error: read tcp *************:52420->**************:56011: wsarecv: An existing connection was forcibly closed by the remote host."
time="2025-08-01T22:38:21.415690600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:38:21.415690600+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-01T22:38:21.416191700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:38:21.416191700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-01T22:38:23.179986500+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-01T22:38:25.245334000+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-01T22:38:28.468993900+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-01T22:38:35.120104600+08:00" level=error msg="[Sniffer] [*********] [tls] may not have any sent data, Consider adding skip"
time="2025-08-01T22:38:38.894669200+08:00" level=warning msg="[TCP] dial 💻 Copilot (match RuleSet/copilot) **********:64194(OneDriveStandaloneUpdater.exe) --> self.events.data.microsoft.com:443 error: read tcp *************:64197->**************:56011: wsarecv: An existing connection was forcibly closed by the remote host."
time="2025-08-01T22:38:38.904815400+08:00" level=warning msg="[TCP] dial 💻 Copilot (match RuleSet/copilot) **********:64195(OneDriveStandaloneUpdater.exe) --> self.events.data.microsoft.com:443 error: read tcp *************:64198->**************:56011: wsarecv: An existing connection was forcibly closed by the remote host."
time="2025-08-01T22:38:39.167061100+08:00" level=warning msg="[TCP] dial 💻 Copilot (match RuleSet/copilot) **********:64195(OneDriveStandaloneUpdater.exe) --> self.events.data.microsoft.com:443 error: read tcp *************:64216->**************:56011: wsarecv: An existing connection was forcibly closed by the remote host."
time="2025-08-01T22:38:39.441421700+08:00" level=warning msg="[TCP] dial 💻 Copilot (match RuleSet/copilot) **********:64195(OneDriveStandaloneUpdater.exe) --> self.events.data.microsoft.com:443 error: read tcp *************:64217->**************:56011: wsarecv: An existing connection was forcibly closed by the remote host."
time="2025-08-01T22:38:40.859071100+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-01T22:39:13.200489300+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-08-01T22:43:41.415979200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:43:41.415979200+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-01T22:43:41.416500600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-01T22:43:41.416500600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-01T22:43:43.305463200+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-08-01T22:43:43.508219700+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-01T22:43:45.950159000+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-01T22:43:49.491651600+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-01T22:43:58.700716700+08:00" level=error msg="MLY | SG - 新加坡 01 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:43:58.700716700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:43:58.773374400+08:00" level=error msg="MLY | SG - 新加坡 02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:43:58.773374400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:44:01.848496200+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-01T22:44:37.208622100+08:00" level=warning msg="[TCP] dial 💻 Cursor (match DomainSuffix/cursor.sh) **********:49496(Cursor.exe) --> api2.cursor.sh:443 error: cn-sh.sasg4e.ccddn4.icu:65301 connect error: context deadline exceeded"
time="2025-08-01T22:44:38.694899800+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-08-01T22:48:40.121744500+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-08-01T22:48:42.366907900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:48:43.480419900+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-08-01T22:48:45.456120200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:48:45.456120200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-01T22:48:46.098251100+08:00" level=warning msg="because 🇹🇼 台湾 failed multiple times, active health check"
time="2025-08-01T22:48:53.432485500+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_3832646131376238 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:48:53.432485500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:48:57.139888900+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-08-01T22:48:57.139888900+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-08-01T22:49:35.379841200+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6635363061343766 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:49:35.379841200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:49:43.918492900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3938356531643832 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:49:43.918492900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:49:50.430505300+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6533343938633961 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:49:50.430505300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:49:59.289215200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6264653634666432 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:49:59.289215200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:49:59.823826400+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-08-01T22:50:14.055030800+08:00" level=error msg="MLY | SG - 新加坡 02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:50:14.055030800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-01T22:50:14.259318000+08:00" level=error msg="MLY | SG - 新加坡 03 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-01T22:50:14.259318000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
